import asyncio
from playwright.async_api import async_playwright

async def check_infogain():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print('Navigating to Infogain careers page...')
            await page.goto('https://www.infogain.com/careers/listing/', wait_until='domcontentloaded')
            await page.wait_for_timeout(5000)  # Wait 5 seconds for content to load
            
            # Check if page loaded
            title = await page.title()
            print(f'Page title: {title}')
            
            # Check for job links with current selector
            current_selector = '//a[contains(@href, "/careers/jobID=")]'
            job_links = await page.locator(current_selector).all()
            print(f'Job links found with current selector: {len(job_links)}')
            
            # Try alternative selectors
            alt_selectors = [
                '//a[contains(@href, "job")]',
                '//a[contains(@href, "career")]',
                '//a[contains(@href, "position")]',
                'a[href*="job"]',
                'a[href*="career"]',
                'a[href*="apply"]',
                '.job-listing a',
                '.career-item a',
                '[data-job-id]'
            ]
            
            for selector in alt_selectors:
                try:
                    links = await page.locator(selector).all()
                    print(f'Links found with "{selector}": {len(links)}')
                    if len(links) > 0:
                        # Get first few links
                        for i, link in enumerate(links[:3]):
                            href = await link.get_attribute('href')
                            text = await link.inner_text()
                            print(f'  Link {i+1}: {href} - {text[:50]}...')
                except Exception as e:
                    print(f'Error with selector "{selector}": {e}')
            
            # Check for any links on the page
            all_links = await page.locator('a').all()
            print(f'Total links on page: {len(all_links)}')
            
            # Check page content for job-related keywords
            content = await page.content()
            keywords = ['job', 'career', 'position', 'opening', 'vacancy']
            for keyword in keywords:
                count = content.lower().count(keyword)
                print(f'Keyword "{keyword}" appears {count} times')
                
            # Check if there are any job-related elements
            job_elements = await page.locator('*:has-text("job"), *:has-text("career"), *:has-text("position")').all()
            print(f'Elements containing job-related text: {len(job_elements)}')
                
        except Exception as e:
            print(f'Error: {e}')
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(check_infogain())
