[{"jd_link": "https://www.techmagic.co/career/vacancies/213467", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Marketing Cloud Consultant", "location": "Ukraine, Brazil, Colombia, Poland Ukraine", "location_type": "remote", "job_type": "full_time", "min_experience": 2, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-marketing-cloud-consultant-9sW/new/", "description": "We are looking for a talented Marketing Cloud Developer with 2+ years of experience to join our project for working on enterprise-level projects from the USA. About ProjectOur customer is one of the world’s most successful hotel companies. Hotel has 118 properties in 47 countries. For the second consecutive year, the hotel was named the Best Luxury Hotel Chain in the world by Business Traveller magazine. REQUIREMENTS:MUST HAVEExperience with Salesforce Marketing Cloud: Email Studio, Automation Studio, & Journey Builder. 2+ years of experience with writing multi-step SQL queries for segmentation. 2+ years of experience with email coding using HTML, CSS, and AMPscript. Strong critical thinking and problem-solving skills, especially for data segmentation, targeting, and email rendering issues. This is important for optimizing campaign performance and troubleshooting technical issues. Ability to manage your time set expectations with stakeholders, and meet deadlines. Enthusiasm for learning in this role, and interest in the email marketing or digital marketing space. . English — Intermediate+. RESPONSIBILITIES:You will be responsible for email development, data queries and segmentation, and Journey Builder. This includes coding emails, ensuring tracking tags are embedded, QA testing, creating queries, automating query activities, and creating or updating email marketing journeys. Email Development: Coding Emails using HTML, CSS, and AMPscript. Ensure all necessary tracking tags are properly embedded into the email. QA & test email via Litmus. Email Deployment for sends,Data Queries & Segmentation: Creating queries for audience segmentation, A/B testing, email sends, and reporting purposes. Creating and automating query activities. Journey Builder: Create or update email marketing journeys. Joining team meetings, including sprint planningWORK SCHEDULE:Full-time working days, flexible hours, and the full remote are available. INTERVIEW STAGES:1. Screen with a recruiter (up to 30 minutes). 2. Technical interview (up to 1 hour). 3. Client interview with (up to 1 hour).", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/213827", "company_id": 2829, "source": 3, "skills": null, "title": "Senior Marketing Cloud Consultant", "location": "Colombia, Brazil, Ukraine, Poland Ukraine", "location_type": "remote", "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/senior-marketing-cloud-consultant-9yW/new/", "description": "We are looking for a talented Senior Marketing Cloud Developer with 4+ years of experience to join our project for working on enterprise-level projects from the USA. About ProjectOur customer is one of the world’s most successful hotel companies. Hotel has 118 properties in 47 countries. For the second consecutive year, the hotel was named the Best Luxury Hotel Chain in the world by Business Traveller magazine. REQUIREMENTS:MUST HAVEExperience with Salesforce Marketing Cloud: Email Studio, Automation Studio, & Journey Builder. 4+ years of experience with writing multi-step SQL queries for segmentation. 4+ years of experience with email coding using HTML, CSS, and AMPscript. Strong critical thinking and problem-solving skills, especially for data segmentation, targeting, and email rendering issues. This is important for optimizing campaign performance and troubleshooting technical issues. Ability to manage your time set expectations with stakeholders, and meet deadlines. Enthusiasm for learning in this role, and interest in the email marketing or digital marketing space. . English — Intermediate+. WILL BE A PLUSLocation in Lviv or KyivRESPONSIBILITIES:You will be responsible for email development, data queries and segmentation, and Journey Builder. This includes coding emails, ensuring tracking tags are embedded, QA testing, creating queries, automating query activities, and creating or updating email marketing journeys. Email Development: Coding Emails using HTML, CSS, and AMPscript. Ensure all necessary tracking tags are properly embedded into the email. QA & test email via Litmus. Email Deployment for sends,Data Queries & Segmentation: Creating queries for audience segmentation, A/B testing, email sends, and reporting purposes. Creating and automating query activities. Journey Builder: Create or update email marketing journeys. Joining team meetings, including sprint planningWORK SCHEDULE:Full-time working days, flexible hours, and the full remote are available. INTERVIEW STAGES:1. Screen with a recruiter (up to 30 minutes). 2. Technical interview (up to 1 hour). 3. Client interview with (up to 1 hour).", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/213623", "company_id": 2829, "source": 3, "skills": null, "title": "Middle strong Salesforce developer", "location": "Colombia, Ukraine Brazil", "location_type": "flexible", "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-strong-salesforce-developer-9vy/new/", "description": "We are looking for an experienced Salesforce Developer with 4+ years of commercial experience to join our MagicFuse team. Requirements:4+ years of commercial experience with SalesforceExperience with Salesforce Lightning Design System, LWC, Lightning Components, ApexStrong understanding of Salesforce platform architecture and best practicesExcellent problem-solving skills and the ability to thrive in a fast-paced, dynamic environmentEffective communication skills and the ability to collaborate with cross-functional teamsAt least an Upper-Intermediate level of EnglishResponsibilities:Refactoring and Enhancement: Collaborate closely with cross-functional teams to refactor the existing website codebase, implementing improvements and new features to elevate user experience and functionalityLeverage your expertise in Apex and Lightning Web Components (LWC) to navigate complex coding challengesIdentify and troubleshoot technical issues efficiently, demonstrating a proactive approach to problem-solving while maintaining a high standard of code qualityWork Schedule: Full-time working day in our office (flexible hours) or remoteInterview stages:1-st stage - call with <PERSON><PERSON>ruiter 2-nd stage - interview with our SF developer and Re<PERSON>ruiter 3-rd stage - Client interview Benefits:Opportunity to work with international clientsPaid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsOne of the best IT employers in Lviv based on DOU ratingRecuiter Yuliia Nochovna", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/215027", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Salesforce administrator", "location": "remote Ukraine", "location_type": "remote", "job_type": "contract", "min_experience": 2, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/magicfuse/jobs/middle-salesforce-administrator-9SW/new/", "description": "We are looking for a talented Middle Salesforce Administrator with 2+ years of commercial experience to join our MagicFuse Team. Requirements 2+ years of experience as a Salesforce Administrator Experience with Sales Cloud, Service Cloud, Validation Rules, Flows At least an Intermediate Strong level of English Must Certifications:Salesforce AdministratorSales Cloud Consultant OR Service Cloud ConsultantWill be a plusSalesforce Advanced Administrator CertificationExperience Cloud Consultant CertificationResponsibilitiesImplement enhancements and customizations to Salesforce configurationsTest and validate new configurations, customizations, and system updates to ensure quality and functionality align with business expectationsManage user profiles, roles, and permissions to ensure that appropriate access levels are maintained for different usersImplement scalable automation solutions using Salesforce Flows and other declarative tools to streamline processes and improve efficiencyCollaborate with Dev team to implement complex solutionsProvide hands-on support for advanced user inquiries, troubleshooting complex issues, and implementing corrective measures promptlyWork ScheduleFull-time working day, the full remote is availableInterview Stages1-st stage — call with Recruiter2-nd stage — interview with our SF Admin and RecruiterOur BenefitsProjects with modern stack Work from anywhere (fully remotely or in our office) Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsOne of the best IT employers in Lviv based on DOU ratingRecruiter Mariia Katrych", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/216200", "company_id": 2829, "source": 3, "skills": null, "title": "Junior Accountant (молод<PERSON>ий бухгалтер)", "location": "Lviv only Ukraine", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/junior-accountant-cbev/new/", "description": "TechMagic в пошуку бухгалтера-початківця, який прагне розвитку та хоче потрапити в нашу професійну та дружню команду. Готові розглянути кандидата без досвіду, адже готові навчати та допомагати у розвитку вас як спеціаліста. *Вакансія відкрита в офісі у Львові та передбачає повну зайнятість. Наші очікування:Високий рівень уважності, відповідальності, вміння швидко реагувати на зміни;Здатність працювати з великим обсягом інформації;Готовність до постійного навчання (зміни в законодавстві — постійні);Базове знання таких програм як Excel, G-suite, інтернет-банкінг;Знання податкового законодавства України. Буде плюсом:Досвід ведення обліку ФОП (на єдиному податку 3 групи);Вища/неповна вища освіта за напрямом «Бухгалтерський облік», «Фінанси», «Економіка»;Знання термінів і правил сплати ЄП, ЄСВ, подання звітності (в тому числі через Електронний кабінет платника податків). Якими будуть задачі:Бухгалтерське обслуговування ФОП на 3й групі єдиного податку;Своєчасне подання звітності ФОП;Робота з реєстрацією / закриттям ФОП;Робота з розрахунками та контролем сплати податків на доходи ФОП;Ведення первинної документації та перевірка її на відповідність вимогам чинного законодавства;Участь в процесі отримання ЕЦП для клієнт-банка. Умови роботи:Стабільна компанія з офіційною оплатою праці;Оплачувана відпустка, лікарняні, державні вихідні, допомога при переїзді;Здоров’я: медичне страхування/компенсація занять спортом/перевірка стану здоров’я;Навчання: чудове середовище для саморозвитку, що включає — тренінги, курси англійської мови, оплачувані сертифікації;Розваги: власна футбольна команда, бюджет на спільні обіди з командою, брендовані подарунки;Гнучкий графік роботи;Комфортний сучасний офіс. Етапи інтерв’ю:Інтерв’ю з рекрутеромІнтерв’ю з рекрутером та бухгалтеромІнтерв’ю з СFO", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/216207", "company_id": 2829, "source": 3, "skills": null, "title": "<PERSON><PERSON>ef Accountant (головний бухгалтер)", "location": "Lviv Ukraine", "location_type": null, "job_type": null, "min_experience": null, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/hief-accountant-cbeC/new/", "description": "Компанія TechMagic в пошуках досвідченого бухгалтера, який/яка прагне розвитку та хоче потрапити в нашу професійну та дружню команду. *Вакансія відкрита в офісі у Львові та передбачає повну зайнятість. Обов’язкові вимоги:Досвід ведення юридичних осіб, зареєстрованих в Україні — від 3 років. Вища освіта за напрямом \"Бухгалтерський облік\", \"Фінанси\", \"Економіка\". Знання податкового законодавства України, що регулює діяльність юридичних осіб на єдиному податку та платників ПнВК. Вміння вести облік доходів/витрат, правильно визначати об'єкт оподаткування. Знання термінів і правил сплати ЄП, ЄСВ, подання звітності (в тому числі через Електронний кабінет платника податків). Досвід роботи з програмами: знання Microsoft Dynamics буде перевагою, M. E. Doc, Excel, інтернет-банкінг. Уміння аналізувати фінансову інформацію, готувати запити та відповіді до/від податкових органів. Високий рівень уважності, відповідальності, вміння швидко реагувати на зміни, стресостійкість. Обов’язки:Ведення бухгалтерського обліку підприємства на спрощеній системі оподаткування (єдиний податок):Забезпечення повного, достовірного та своєчасного відображення господарських операцій в обліку. Контроль відповідності діяльності вимогам податкового законодавства щодо єдиного податку. Формування та подання обов’язкової фінансової та податкової звітності. Ведення бухгалтерського обліку підприємства - резидента ДіяСіті, платника податку на прибуток на особливих умовах:Забезпечення повного, достовірного та своєчасного відображення господарських операцій в обліку. Контроль відповідності діяльності вимогам податкового законодавства щодо податку на прибуток на особливих умовах (податок на виведений капітал). Формування та подання обов’язкової фінансової та податкової звітності. Щорічний супровід аудиту резидента ДіяСіті. Організація фінансового моніторингу на підприємстві:Побудова та підтримка внутрішніх процедур фінансового контролю. Систематизація фінансової документації, забезпечення її відповідності чинному законодавству. Підготовка до внутрішніх і зовнішніх перевірок, взаємодія з аудиторами, контролюючими та банківськими структурами в рамках фінансового моніторингу. Умови роботи:Стабільна компанія з офіційною оплатою праці;Оплачувана відпустка, лікарняні, державні вихідні, допомога при переїзді;Здоров’я: медичне страхування/компенсація занять спортом/перевірка стану здоров’я;Навчання: чудове середовище для саморозвитку, що включає — тренінги, курси англійської мови, оплачувані сертифікації;Розваги: власна футбольна команда, бюджет на спільні обіди з командою, брендовані подарунки;Гнучкий графік роботи;Комфортний сучасний офіс. Етапи інтерв’ю:Інтерв’ю з рекрутеромІнтерв’ю з рекрутером та бухгалтеромІнтерв’ю з СFO та СЕОРекрутер - Anastasiia Muzychuk", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/216572", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Strong Python developer", "location": "Lviv or Kyiv Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-strong-python-developer-cbkH/new/", "description": "We are looking for a skilled Middle Strong Python Developer to join a fast-growing fintech product team building an AI-powered platform that helps finance teams gain real-time cash visibility, automate reconciliation, and forecast cash flow—all powered by AI. Requirements: 4+ years of experience as a backend engineer on large-scale projectsStrong Python 3. x skills (3+ years)Experience with FastAPI, Flask, or DjangoKnowledge of SQL and NoSQL databases (PostgreSQL, Redis)Experience with AWS cloud infrastructureFamiliarity with API design and implementationSolid understanding of software architecture, clean code principles, and testingIntermediate English level or higherWill be a plus: Experience in microservices architectureFamiliarity with Kubernetes and container orchestrationKnowledge of CI/CD tools (e. g. , GitHub Actions, Jenkins)Background in fintech or treasury management systemsUnderstanding of event-driven architectures and data streaming with KafkaExposure to AI/ML technology integrationsResponsibilities: Design and develop robust, scalable backend servicesArchitect cloud-native solutions in a distributed environmentCollaborate with data science teams to integrate AI into core featuresBuild and maintain RESTful APIs and microservicesParticipate in code reviews and provide technical feedbackWork closely with the product and customer success teams to understand and solve user challengesCooperate with cross-functional teams including frontend, DevOps, and productProductThe platform replaces Excel-based workflows with automated, real-time visibility into cash positions. Backed by top-tier VCs with $10M+ in funding, the team is building cutting-edge tools to support better financial decision-making. Work Schedule: Full-time working day in our office (flexible hours) or remoteInterview stages:1-st stage — call with the Recruiter2-nd stage — interview with the Python developer and the Recruiter3-rd stage — client interviewBenefits:Projects with modern stackWork from anywhere (fully remotely or in our office) Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsRecruiter Mariia Katrych", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/216573", "company_id": 2829, "source": 3, "skills": null, "title": "Senior Python developer", "location": "Lviv or Kyiv Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/senior-python-developer-cbkI/new/", "description": "We are looking for a skilled Senior Python Developer to join a fast-growing fintech product team building an AI-powered platform that helps finance teams gain real-time cash visibility, automate reconciliation, and forecast cash flow—all powered by AI. Requirements: 5+ years of experience as a backend engineer on large-scale projectsStrong Python 3. x skills (3+ years)Experience with FastAPI, Flask, or DjangoKnowledge of SQL and NoSQL databases (PostgreSQL, Redis)Experience with AWS cloud infrastructureFamiliarity with API design and implementationSolid understanding of software architecture, clean code principles, and testingIntermediate English level or higherWill be a plus: Experience in microservices architectureFamiliarity with Kubernetes and container orchestrationKnowledge of CI/CD tools (e. g. , GitHub Actions, Jenkins)Background in fintech or treasury management systemsUnderstanding of event-driven architectures and data streaming with KafkaExposure to AI/ML technology integrationsResponsibilities: Design and develop robust, scalable backend servicesArchitect cloud-native solutions in a distributed environmentCollaborate with data science teams to integrate AI into core featuresBuild and maintain RESTful APIs and microservicesParticipate in code reviews and provide technical feedbackWork closely with the product and customer success teams to understand and solve user challengesCooperate with cross-functional teams including frontend, DevOps, and productProductThe platform replaces Excel-based workflows with automated, real-time visibility into cash positions. Backed by top-tier VCs with $10M+ in funding, the team is building cutting-edge tools to support better financial decision-making. Work Schedule: Full-time working day in our office (flexible hours) or remoteInterview stages:1-st stage — call with the Recruiter2-nd stage — interview with the Python developer and the Recruiter3-rd stage — client interviewBenefits:Projects with modern stackWork from anywhere (fully remotely or in our office) Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsRecruiter Mariia Katrych", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/218727", "company_id": 2829, "source": 3, "skills": null, "title": "Machine Learning/AI Specialist (part-time, 10-15 hours per week)", "location": "Remote Ukraine", "location_type": null, "job_type": "full_time", "min_experience": 4, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/machine-learningai-specialist-part-time-10-15-hours-per-week-cbUC/new/", "description": "A centre of excellence (COE) is a team of senior tech experts who conduct research on the strategic tech trends, maintain top quality standards, foster professional development, and can provide you with expert consulting throughout the whole development process. The CoE helps our clients to perform a Product Discovery Phase, build a prototype, and validate a product concept. The experts provide a client with a High-Level Architecture plan, the suggested technology stack, the product design, time and cost estimates, consulting, and a Work Breakdown Structure. Now, we are looking for a skilled AI/ML specialist, preferably with some data science experience, who will contribute to establishing and continuously improving the AI/ML expertise in the CoE department and the company overall. The role is ideal for a professional capable of effectively researching, proposing, and implementing ML/AI solutions and strategies corresponding to clients' needs. RequirementsMust have4+ years of experience as an ML engineer, AI specialist or a similar roleHands-on experience with AI technologies, including generative AI, computer vision, LLMs and/or othersProficiency in ML frameworks (Tensorflow, scikit or alternatives)Familiarity with deploying AI/ML models to cloud providers such as AWS or GoogleKnowledge of data visualisation toolsStrong programming skills in PythonWould be a plus: experience in web developmentWould be a plus: background in data science, data analytics or data engineeringWould be a plus: education in a relevant field (mathematics, computer science, statistics, software engineering, etc. )Strong communication and presentation skillsUpper-Intermediate spoken and written EnglishBusiness-oriented mindset and ability to understand real goals of the clientAbility to document solutions and share the knowledge effectivelyWould be a plus: Skills in team leadership or mentorshipWould be a plus: Public speaking experienceResponsibilities:Train predictive models based on existing/historical dataPost-train or fine-tune existing models (e. g. from HuggingFace) to achieve the requested functionalityBuild a prototype of an AI-driven analytics tool during the discovery phaseValidate client's idea and provide feedback on its possibility of implementation during pre-sales activitiesPrepare an estimation (time and cost) of the implementation of a particular project or featureJoin and investigate an ongoing project with a purpose such as: consulting, development or super-vising a particular process. Interview StagesCall with RecruiterTechnical interview with Recruiter and Head of CoEOur BenefitsWork from anywhere (fully remotely or in our offices in Lviv, Kyiv or Krakow)Paid vacations and sick leaves, additional days off, relocation bonusEducation: regular tech talks, educational courses, paid certifications, English classesRecruiter - Yuliia Nochovna", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/219261", "company_id": 2829, "source": 3, "skills": null, "title": "AI Specialist (LLMs, Ollama)", "location": "Lviv Ukraine", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/httpsmagichireco/jobs/ai-specialist-llms-ollama-cb4w/new/", "description": "We're looking for an AI Specialist to expand our client`s team in Lviv! This is a full-time, on-site position with a leadership role in applied research and developing open-source LLM models for a large enterprise customer. You'll integrate and optimise models deployed via Ollama, pushing the boundaries of enterprise AI use cases. Responsibilities:Shape our GenAI & classical ML vision. Advise on build-vs-buy for LLM stacks, vector DBs, and MLOps tooling. Address similarity & normalisationDesign deep-learning + rule pipelines to match ~250 M address records/year. Evaluate with ragas and bespoke geo-accuracy metrics. Customer predictions Engineer features from parcel scans, seasonal peaks, traffic data. Train and ship predictive models (ETA, delivery-success likelihood) instrumented with Langfuse. Semantic search & RAG Implement RAG on FAQs, SOPs, and support tickets using open-source LLMs (e. g. , Mixtral, Phi-3) served via Ollama. Optimise embedding stores, latency, and cost for call-centre workloads. Production excellenceDeploy models to Kubernetes/AWS with CI/CD, monitoring, canary roll-outs. Requirements:Open-source LLMs: Familiarity with open-source LLMs (LLaMA, Mistral, Phi, etc. ). Deployment Tools: Experience using or deploying via Ollama, Hugging Face, or similar tools. Independence & Communication: Ability to work independently and communicate clearly in English. Research Mindset: Strong research mindset, hands-on coding ability, and documentation skills. What We Offer:Leadership Role: A leadership role in an applied AI initiative for an international enterprise. Cutting-edge Tools: Work with cutting-edge tools like Ollama, RAG pipelines, and low-latency deployments. Talented Team: A talented, motivated team. Competitive Compensation: Competitive compensation and long-term opportunity. Location: Lviv, Ukraine | On-site, Full-time", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/219690", "company_id": 2829, "source": 3, "skills": null, "title": "Fullstack developer with AI experience (Center of Excellence Member)", "location": "remote Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 5, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/fullstack-developer-with-ai-experience-center-of-excellence-member-cccF/new/", "description": "This position is open in the Center of Excellence (CoE) department at TechMagic. You’ll join a team of senior tech experts working on cutting-edge initiatives in AI and web technologies. The CoE focuses on technical consulting, building prototypes, performing discovery phases, and solving non-trivial challenges for various international clients. We’re looking for a strong Fullstack Developer / Solution Architect who is passionate about building smart, AI-driven applications, thrives on research and rapid prototyping, and is eager to shape the future of web development through the use of modern technologies and LLMs. Must have5+ years of experience in full-stack developmentProficiency with JavaScript and TypeScriptExperience with cloud platforms (preferably AWS, but others are acceptable)Hands-on experience working with LLMs and other AI-related servicesExperience building web applications and APIs (React/Angular/Vue + Node. js or similar)Strong problem-solving and research skillsAbility to communicate complex technical topics clearly to both technical and non-technical stakeholdersEnglish proficiency — Upper-Intermediate or higherWill be a huge plusExperience in building AI-driven or agentic applicationsExperience in integrating various third-party AI/ML services and APIsFamiliarity with vector databases, RAG, or AI orchestration toolsResponsibilitiesOperate as a core technical expert in pre-sale and early-stage project discovery phasesResearch and validate AI technologies and integration approachesDesign and build MVPs, prototypes, and proof-of-concept solutionsProvide technical consulting and mentorshipDevelop both frontend and backend featuresContribute to cloud architecture and deployment workflowsCollaborate with clients, stakeholders, and cross-functional teamsStay updated with emerging trends in AI and software architectureAbout ProjectA center of excellence (COE) is a team of senior tech experts who conduct research on strategic tech trends, maintain top quality standards, foster professional development, and can provide you with expert consulting throughout the whole development process. The CoE helps our clients to perform a Product Discovery Phase, build a prototype, and validate a product concept. The experts provide a client with a High-Level Architecture plan, the suggested technology stack, the design of the product, time and cost estimates, consulting, and a Work Breakdown Structure. Furthermore, CoE provides tech assessment and consulting in case of a bottleneck or a non-trivial problem on existing projects. Work ScheduleFull-time working day in our offices (flexible hours) or full-time remoteInterview Stages1-st stage — call with Recruiter2-nd stage — interview with our Senior Full Stack developerOur BenefitsProjects with modern JS stack (React. js, React Native, Angular, Node. js)Strong JavaScript community at the company (50+ developers)Work from anywhere (fully remotely or in our office)Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsOne of the best IT employers in Lviv based on DOU ratingRecruiter Mariia Katrych", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/222013", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Manual QA (working schedule 15.00-24.00)", "location": "Lviv or Kyiv Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 5, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-manual-qa-working-schedule-1500-2400-ccPo/new/", "description": "We are looking for a proactive Middle QA engineer with 2+ years of experience to join our startup from the USA, California. RequirementsMust have2+ years of experience as a QA EngineerSolid knowledge of testing theory: test processes, test design techniques, and test documentationExperience in mobile and web application testing, API testing (using Postman), and working with databases (such as Firestore DB and PostgreSQL)Experience in creating test documentation, including checklists, test cases, test runs, and bug reports, using tools such as Jira, Confluence, and TestRailKnowledge of Scrum team processes, roles, and ceremoniesAbility to quickly understand complex tasks, participate in design and requirements reviews and work closely with Product Owners, Developers, Designers, and other QAsEnglish level - Upper-Intermediate Will be a plusExperience working with Sentry, A/B testing tools, and testing of the payment systems on the integration level. ResponsibilitiesAll types of manual testing for our mobile applications and webRequirements reviewBug hunting & reportingWriting of test documentationParticipation in Scrum meetingsActive participation in the development and release processCustomerUSA, CaliforniaProductOur product helps families with children sleep better. We combine AI and real experts to make it affordable and easy to use. We started because we know how hard it is for parents when their kids don't sleep well. Our team includes sleep experts and data scientists who came together to solve this problem. Our first product, helps kids from newborns to 5 years old sleep better and longer. Once we understand your child's sleep patterns, our experts and algorithms determine what will help. It also tracks baby sleep, pumping, feeding, diaper changes, and growth all in one place. Our achievements for now:Around +100,000 downloads of the app, excellent reviews16. 1M FundingGet started at iOS, AndroidStageThis is a growing startupProject teamClient-side: CTO, Product Manager, Lead Full-stack developer, 2 Full-stack Developers, 6 Front-end developers; Lviv office: Lead QA Engineer, 5 Manual QA, 2 AQA Engineers, 5 Front-end developers, 1 PMProject TechnologiesNode, Python, Firebase, Angular 9, Ionic 5Work ScheduleFull-time working day in our office (Lviv or Kyiv, flexible hours) or full-time remoteInterview Stages1-st stage — Сall with Recruiter (30 minutes)2-nd stage — Interview with QA Engineer and Recruiter (1 hour)Recruiter Anastasiia Muzychuk", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/219691", "company_id": 2829, "source": 3, "skills": null, "title": "Senior Node.js developer", "location": "Lviv, Kyiv, remote Ukraine", "location_type": null, "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/httpsmagichireco/jobs/senior-nodejs-developer-cccG/new/", "description": "We're looking for a Senior Node. js Developer with 3+ years of experience to expand our client's team in Lviv! This is a full-time, on-site position for an experienced developer who will lead the development of high-quality backend services and microservices, ensuring their performance, scalability, and security. Responsibilities: Backend Services Development & Maintenance: Develop and maintain high-quality backend services using Node. js and related technologies. API Design & Optimization: Design, implement, and optimize RESTful APIs and microservices. Database Management: Efficiently work with databases such as PostgreSQL, MySQL, Oracle, or MongoDB to manage application data. Cross-Functional Collaboration: Collaborate with frontend developers, designers, and product managers to create seamless user experiences. Quality Assurance: Ensure application performance, scalability, and security. Testing & Debugging: Perform unit testing, integration testing, and debugging of applications. Code Improvement: Participate in code reviews and contribute to continuous improvement efforts. Requirements: Node. js Experience: 3+ years of experience in backend development using Node. js. Framework Proficiency: Proficiency with Express. js, NestJS, or TSOA frameworks. Languages & Paradigms: Strong knowledge of JavaScript, TypeScript, and asynchronous programming. Database Experience: Experience working with SQL and NoSQL databases (PostgreSQL, MySQL, Oracle, MongoDB, etc. ). API & Security: Understanding of RESTful API design and web security best practices. Containerization & Cloud Platforms: Familiarity with containerization and cloud platforms (Docker, Kubernetes, AWS, Azure, or GCP). CI/CD & DevOps: Experience with CI/CD pipelines and DevOps practices is a plus. Teamwork: Ability to work effectively as a team player in a collaborative environment. English Communication: Strong communication skills in English (must-have). What We Offer:Impactful Role: An opportunity to make a significant contribution to the development of high-quality software solutions. Modern Technologies: Work with current technologies and tools in the Node. js ecosystem. Talented Team: Work in a talented and motivated team. Competitive Compensation: Competitive salary and long-term cooperation opportunities. Interview Stages:Call with RecruiterTechnical InterviewClient InterviewRecuiter Yuliia Nochovna", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/222249", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Manual QA Engineer", "location": "Lviv or Kyiv Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 2, "max_experience": 2, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-manual-qa-engineer-ccTk/new/", "description": "We are looking for an experienced Middle QA engineer with 2+ years of experience to join our biggest project in MagicFuse. We consider candidates from Lviv or Kyiv. RequirementsMust haveAt least 2 years of relevant experienceStrong knowledge of SDLC and STLCStrong knowledge and experience in different testing types and methodsExperience with test management and bug reporting toolsExperience in creating and executing test plans and test casesExperience in test reportingUnderstanding of risk managementPractical knowledge of REST servicesKnowledge of SQLStrong analytical and problem-solving skillsLeadership and management capabilitiesAt least an Intermediate level of EnglishWill be a plusExperience with SalesforceResponsibilitiesCreating Test documentationParticipating in project planning and requirements analysisConducting functional testing Preparing test reportsAbout ProjectProductOur customer is one of the world’s most successful hotel companies. The hotel has 118 properties in 47 countries. For the second consecutive year, the hotel was named the Best Luxury Hotel Chain in the world by Business Traveller magazine. Project teamLviv team: 35 people. Work ScheduleFull-time working day in our office (flexible hours) or remote. Interview Stages1-st stage — call with <PERSON><PERSON><PERSON><PERSON> (30 minutes)2-nd stage — interview with our QA Lead and <PERSON><PERSON>ruit<PERSON> (1 hour)3-rd stage — Client interviewOur benefitsOpportunity to improve your skills in manual and automation testing;Work from anywhere (fully remotely or in our office) Paid vacations and sick-leaves, additional days-off, relocation bonus;Wellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classes;Fun: own football team, budget for team-lunches, branded gifts. Recruiter Viktoria Kliushta", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/222268", "company_id": 2829, "source": 3, "skills": null, "title": "Salesforce Architect (part-time, 10+ hours per week)", "location": "remote Ukraine", "location_type": "flexible", "job_type": "part_time", "min_experience": 4, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/salesforce-architect-part-time-10-hours-per-week-ccTD/new/", "description": "We are looking for experienced Salesforce Solution Architects (a few experts) to join our team on a part-time basis (10+ hours/week). This role is ideal for a senior expert who wants to stay hands-on with Salesforce implementations while contributing strategically to architecture, integration, and delivery excellence across complex ecosystems. We’re specifically seeking professionals with deep expertise in one or more of the following areas: Data Cloud, Spiff, Revenue Cloud Advanced, CPQ, Mulesoft, Mulesoft IDP, Agentforce, Commerce Cloud, Marketing Cloud Growth, Certinia, Informatica. You do not need to have experience in all listed technologies — we value specialized knowledge and are assembling a team of architects with complementary areas of expertise. Key ResponsibilitiesGather and analyze client requirementsLead discovery and estimation phases of Salesforce projectsDefine scalable, secure, and extensible solution architecturesDrive platform alignment across multiple Salesforce cloudsDesign integration solutions using Mulesoft and related technologiesOversee and support custom development effortsPresent solutions, lead MVP and POC deliveryProvide technical leadership during project initiation and executionSupport pre-sales efforts through solutioning and proposal inputOffer best practice advisory and technology governanceDevelop and deliver training for client and internal teamsPlan and supervise data migration activitiesCoach internal team members and conduct technical interviewsRun specialized training programs related to assigned technologiesRequirements- 7+ years in the Salesforce ecosystem, with 4+ years of hands-on experience in one or several key fields (Data Cloud, Spiff, Revenue Cloud Advanced, CPQ, Mulesoft, Mulesoft IDP, Agentforce, Commerce Cloud, Marketing Cloud Growth, Certinia, Informatica)- Proven experience delivering enterprise-grade solutions- Strong portfolio or relevant project references- Architect-level Salesforce certifications (preferred)- Familiarity with version control and DevOps tools (e. g. , Git, Copado, Gearset)- Agile/Scrum delivery experience- Experience with multi-cloud Salesforce implementationsSoft Skills & Work Environment- Excellent written and verbal English communication- Ability to explain technical topics to non-technical stakeholders- Proactive communication and strong team collaboration- Comfortable working in distributed, multicultural environments- Availability for daily standups, sprint planning, and retrospectives- Minimum 2 hours/day overlap with EET time zoneJoin us to architect modern, future-proof Salesforce solutions while maintaining a flexible, part-time schedule with a high-impact team! Recuiter - Viktoria Kliushta", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/223184", "company_id": 2829, "source": 3, "skills": null, "title": "PPC Specialist", "location": "remote Ukraine", "location_type": null, "job_type": "full_time", "min_experience": null, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/ppc-specialist-cc9T/new/", "description": "We’re looking for a skilled PPC Expert to join TechMagic’s marketing team. Your primary mission will be driving targeted paid traffic, generating qualified leads, and maximizing conversion rates across key platforms such as Google Ads, LinkedIn Ads, and optionally Bing Ads. You’ll play a critical role in scaling our demand generation efforts and fueling business growth. If you’re ready to take charge of PPC campaigns that drive real business impact, apply now and be a part of TechMagic’s growth! Key Responsibilities:Plan, create, and optimize PPC campaigns on Google Ads and LinkedIn Ads (Bing Ads experience is a plus)Manage budgets efficiently to maximize ROI and lead qualityPerform keyword research, audience targeting, and competitor analysis to improve campaign performanceContinuously monitor, analyze, and report on campaign KPIs and conversion metricsCollaborate with content, design, and sales teams to align messaging and landing page optimizationTest new ad formats, bidding strategies, and platform features to innovate and improve resultsStay updated on PPC trends and platform updates to maintain a competitive advantageQualifications:Proven experience managing PPC campaigns on Google Ads and LinkedIn Ads for B2BResult-oriented mindsetSolid understanding of paid traffic funnels, lead generation, and conversion optimizationFamiliarity with Bing Ads is a plus, but not requiredStrong analytical skills and experience with campaign tracking tools (Google Analytics, Google Tag Manager, LinkedIn Campaign Manager)Ability to work independently and as part of a cross-functional teamExcellent communication skills and attention to detailInterview stages:Call with RecruiterTest taskInterview with Recruiter and Marketing ManagerInterview with Head of MarketingOur benefitsOpportunity to work with international clientsWork from anywhere (fully remotely or in our office) Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsRecruiter - Anastasiia Vaskiv", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/223458", "company_id": 2829, "source": 3, "skills": null, "title": "Middle strong/Senior Node.js developer with AI experience", "location": "Lviv or Kyiv, or remote Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-strongsenior-nodejs-developer-with-ai-experience-cdft/new/", "description": "We're looking for a Middle Strong/Senior Node. js Developer with 3+ years of experience to join our team and contribute to an innovative project focused on transforming HR operations with AI! This is a fantastic opportunity to work on a platform that integrates with existing HR systems, handles a significant volume of employee requests, and automates various HR tasks, ensuring 24/7 personalized support across multiple communication channels. Must have:Strong experience with TypeScript, Node. js, and PostgreSQL. Proven experience with AI/LLM integration. Proficiency in designing and developing scalable backend services. Excellent problem-solving skills and a strong commitment to writing clean, maintainable code. Advanced English communication skills. Will be a plus:Experience with Microservices architecture;Responsibilities:Develop and maintain high-quality backend services using Node. js and TypeScript. Design and implement robust integrations with AI/LLM models to enhance platform capabilities. Work with PostgreSQL to manage and optimize application data. Collaborate with cross-functional teams to deliver seamless and efficient HR solutions. Ensure the scalability, performance, and security of the platform. Participate in code reviews, contribute to architectural discussions, and drive continuous improvement. About the Project:This AI-driven HR Coordinator platform helps companies streamline and scale their HR operations. It integrates seamlessly with existing HRIS systems and knowledge bases, capable of handling up to 70% of employee requests – covering everything from policy and payroll questions to benefits and processes. The platform also automates crucial tasks like updating HR records, drafting documents, managing approvals, and coordinating communications. Working across tools such as Slack, Teams, email, SMS, and WhatsApp, it provides round-the-clock personalized support, escalates complex issues to the appropriate personnel, and delivers valuable insights into HR operations. Interview Stages1-st stage — call with Recruiter2-nd stage — interview with our Senior developer3-rd stage - client interviewOur BenefitsProjects with modern JS stack (React. js, React Native, Angular, Node. js)Strong JavaScript community at the company (50+ developers)Work from anywhere (fully remotely or in our office)Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsOne of the best IT employers in Lviv based on DOU rating Recruiter — Yuliia Nochovna", "ctc": null, "currency": null, "meta": {}}, {"jd_link": "https://www.techmagic.co/career/vacancies/223448", "company_id": 2829, "source": 3, "skills": null, "title": "Middle Node.js developer, expert in Automation with AI", "location": "Lviv or Kyiv, remote Ukraine", "location_type": "flexible", "job_type": "full_time", "min_experience": 3, "max_experience": null, "apply_link": "https://techmagic.talentlyft.com/jobs/middle-nodejs-developer-expert-in-automation-with-ai-cdfj/new/", "description": "This position is open in the Centre of Excellence (CoE) department at TechMagic. You’ll join a team of senior tech experts working on cutting-edge initiatives in AI and web technologies. The CoE focuses on technical consulting, building prototypes, performing discovery phases, and solving non-trivial challenges for international clients. We’re looking for a Middle Node,js developer who is passionate about building smart, AI-driven applications, thrives on research and rapid prototyping, and is eager to shape the future of web development using modern technologies and LLMs. Must have:3+ years of experience in full-stack development;Proficiency with JavaScript, Node. js, TypeScript;Experience with workflow automation tools (n8n, pipedream or alternative);Experience with AI services, LLMs and their integration;Understanding of some techniques such as: RAG, Re-ACT or othersEnglish proficiency — Upper-Intermediate or higherWill be a huge plus:Familiarity with frameworks such as LangChain or LlamaIndex;Understanding of the MCP;Familiarity with AWS or alternative cloud providers;Knowledge of any major front-end frameworkResponsibilities:Design, implement and maintain backend services mainly focused on automation of various workflows using AI services, automation platforms, LLM calls chaining, etc. Work with 3rd-party APIs (REST, GraphQL) to interconnect different tools and services. Research and develop proof of concepts. About ProjectA center of excellence (COE) is a team of senior tech experts who conduct research on strategic tech trends, maintain top quality standards, foster professional development, and can provide you with expert consulting throughout the whole development process. The CoE helps our clients to perform a Product Discovery Phase, build a prototype, and validate a product concept. The experts provide a client with a High-Level Architecture plan, the suggested technology stack, the design of the product, time and cost estimates, consulting, and a Work Breakdown Structure. Furthermore, CoE provides tech assessment and consulting in case of a bottleneck or a non-trivial problem on existing projects. Work ScheduleFull-time working day in our offices (flexible hours) or full-time remoteInterview Stages1-st stage — call with Recruiter2-nd stage — interview with our Senior developerOur BenefitsProjects with modern JS stack (React. js, React Native, Angular, Node. js)Strong JavaScript community at the company (50+ developers)Work from anywhere (fully remotely or in our office)Paid vacations and sick-leaves, additional days-off, relocation bonusWellness: Medical insurance/ sport compensation/ health check-up+flu vaccination at your choiceEducation: regular tech-talks, educational courses, paid certifications, English classesFun: own football team, budget for team-lunches, branded giftsOne of the best IT employers in Lviv based on DOU ratingRecruiter — Yuliia Nochovna", "ctc": null, "currency": null, "meta": {}}]