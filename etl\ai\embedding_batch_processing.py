import vertexai
import os
from vertexai.preview import language_models
from google.oauth2 import service_account
from google.cloud import aiplatform
import time
import json

from config.core.settings import get_settings
class GenAIBatchEmbedding:
    def __init__(self):
        self.settings = get_settings()
        self.creds = self._load_credentials(self.settings.GCP_CONFIG_PATH)
        self.client = vertexai.init(project=self.settings.GCP_PROJECT_ID, location=self.settings.GCP_PROJECT_LOCATION,
                                    experiment="jds-embedding",credentials=self.creds)
        self.embedding_model = language_models.TextEmbeddingModel.from_pretrained(
            "text-embedding-005",
        )
    
    def _load_credentials(self, config_path: str) -> service_account.Credentials:
        """Load GCP credentials from config file"""
        config = json.load(open(config_path, 'r'))
        return service_account.Credentials.from_service_account_info(config)

    async def run_batch_embedding_async(self, input_uri:str|list[str]):
        """Start a batch embedding job without waiting for completion"""
        destination_uri_prefix = self.settings.gs_output_path("embedding")
        arguments = {}
        first_input_uri = input_uri if isinstance(input_uri, str) else input_uri[0]
        if first_input_uri.startswith("gs://"):
            if not isinstance(input_uri, str):
                if not all(uri.startswith("gs://") for uri in input_uri):
                    raise ValueError(
                        f"All URIs in the list must start with 'gs://': {input_uri}"
                    )
            arguments["gcs_source"] = input_uri
        # elif first_input_uri.startswith("bq://"):
        #     if not isinstance(input_uri, str):
        #         raise ValueError(
        #             f"Only single BigQuery source can be specified: {input_uri}"
        #         )
        #     arguments["bigquery_source"] = input_uri
        else:
            raise ValueError(f"Unsupported source_uri: {input_uri}")

        if destination_uri_prefix.startswith("gs://"):
            arguments["gcs_destination_prefix"] = destination_uri_prefix
        # elif destination_uri_prefix.startswith("bq://"):
        #     arguments["bigquery_destination_prefix"] = destination_uri_prefix
        else:
            raise ValueError(f"Unsupported destination_uri: {destination_uri_prefix}")

        model_name = self.embedding_model._model_resource_name
        
        # Create the job with sync=False
        # self.embedding_model.batch_predict()
        job = aiplatform.BatchPredictionJob.create(
            model_name=model_name,
            job_display_name=f"embedding-job-{int(time.time())}",
            **arguments,
            model_parameters=None,
            # if you send sync=False, the job will start synchronously only, it occupy the cpu of the machine
            sync=False # make the call async
        )
        job.wait_for_resource_creation()
        return job

