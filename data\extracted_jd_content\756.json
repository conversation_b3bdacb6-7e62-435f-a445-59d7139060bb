[{"jd_link": "https://www.dice.com/job-detail/264f9283-d247-40d0-96ca-98ea440907d7", "company_id": 756, "source": 3, "title": "Software Engineer\nCreate your free profile to continue\nLog in to continue\nConsent to Cookies & Data processing", "location": "", "job_posted_date": "2025-06-06 07:09:13.308075+00:00", "location_type": "hybrid", "job_type": null, "min_experience": 2, "max_experience": 6, "apply_link": "https://www.dice.com/job-detail/264f9283-d247-40d0-96ca-98ea440907d7", "description": "Location: Hyderabad, India (Hybrid)This is a hybrid position primarily based in Hyderabad, India. We're committed to your flexibility and wellbeing and our hybrid strategy currently requires three days a week in the office, giving you the option to work remotely for some of your working week. Find out more about our culture of flexible working. We give you a world of potential. Support is awesome in the way trust makes it work! When you join this dynamic team as a Software Engineer, you will enjoy a career, teamwork, flexibility, and leadership you can trust to help accelerate your personal and professional goals. Come be a part of a world of potential at Computershare Business Support Services. Corporate Trust is a market leader with decades of experience as a provider of trustee and sophisticated agency services for private and public companies, investment bankers, asset managers as well as governments and institutions. We offer a wide range of services that fulfil our clients with a best-in-class reputation built on our high-touch approach to client service we are looking for people to join us and be a part of our exciting future as one of the top corporate trust firms globally. A key part of this role will be collaborating with our onshore teams to service our Corporate Trust business lines and help us to deliver the professional services our clients trust and depend on. If you're a match to those skills and have the passionate drive to be part of something truly amazing, while working on a diverse team and have the willingness to learn multiple tasks, then this is the perfect opportunity for you! A role you will love This role will work within an Agile environment to develop and support applications across the Computershare portfolio. This role will lead moderately complex initiatives and deliverables within technical domain. This role will work within cross-functional teams, this role requires strong technical skills, curiosity, a passion for delivering quality solutions and the drive to continually improve the quality and speed with which we deliver value to the business. This role will resolve moderately complex issues ad lead a team to meet existing client needs and/or potential clients needs while leveraging solid understanding of function, policies, procedures or compliance requirements. In Technology Services (CTS) we partner with our global businesses, providing technology services and IT support, designing, and developing new products to support our clients, customers, and employees. These business-aligned CIO teams leverage the expertise and capacity of enterprise-wide teams, such as the Digital Foundry, the Global Development team and many of our CTO teams. To continually improve our capabilities and speed to market, we have our own innovation, product management and manufacture practices and frameworks which are regularly refined. We ensure that colleagues around the world have access to the technology and agreed service levels that they need to take care of their clients and their clients' shareholders, employees, and customers. Some of your key responsibilities will include:Apply knowledge of standards, policies, best practice and organizational structure so that you can work both independently and collaboratively within your team and with key stakeholders. Provide informal guidance and share knowledge with colleagues to enable them to contribute to the team's objectives. Ensure the quality of tasks, services and information provided by your team - through the quality of your own work and the support you provide to others - to ensure that your team delivers high-quality, maintainable software which adheres to internal standards and policiesSupport the evaluation and resolution of technical challenges and blockers to minimize their impact on the team's delivery and/or supported products. Identify and support improvements and innovation in technologies/practices within your team that would benefit the business e. g. efficiency in the software development process or improved customer experience. What will you bring to the role? We are a global business with an entrepreneurial spirit, and we are proud of that. What that comes with this is a fast-paced environment and lots of change so you will be resilient in nature and able to adapt quickly and embrace the pace of change we often work at. We are looking for people with these skills. The Software Engineer with minimum 3-6 years of work experience in software development and who have strong technical skills in Java/J2EE technologies in order to work collaboratively within their team on the design, build and deployment of software change. 3+ years of Software Engineering experience, or equivalent demonstrated through one or a combination of the following: work experience, training, military experience, education3+ years of experience in Java/J2EE development2+ year(s) experience in any of the following front end technologies: JavaScript, JSON, CSS, JQuery, Ajax and HTML 5, React, Angular3+ years of JAVA Web Services Development using SOAP or RESTFul Web Services/ MicroServices3+ years of experience with Spring, Spring Batch, and Spring Boot3+ years of experience working with Relationship Database Management Systems (RDBMS) such as SQL Server, Oracle or MySQL3+ years of experience in JPA2+ years of Hibernate, Autosys experience, Git or GitHub, Agile2+ year(s) of experience in Test Automation Frameworks like SeleniumCollaborates and communicates well, builds great working relationships, influences others, challenges effectively and responds well to challenge from others, shares information and ideas with others, has good listening skills. Has a strong work ethic and is able to deal with sometimes conflicting priorities. Curious and continuous learner - investigates, interprets and grasps new concepts. Self-motivated and can use own initiative to work with limited guidance to implement innovative solutions. Pays attention to detail, finds root cause and takes a rigorous approach to problem solving. Rewards designed for youHealth and wellbeing rewards that can be tailored to support you and your family. Save for your future. We will support you along your retirement savings journey. Paid parental leave, flexible working and a caring and inclusive culture. Income protection. To ease concerns when the unexpected occurs our package includes short and long-term disability benefits, life insurance, supplemental life insurance (single/spouse/family) and more. And more. Ours is a welcoming and close-knit community, with experienced colleagues ready to help you grow. Our careers hub will help you find out more about our rewards and life at Computershare, visit computershare. com/careershub. Employers have access to artificial intelligence language tools (“AI”) that help generate and enhance job descriptions and AI may have been used to create this description. The position description has been reviewed for accuracy and Dice believes it to correctly reflect the job opportunity. Report this jobDice Id: RTX14cf75Position Id: 1_7321Posted 18 days ago", "ctc": null, "currency": null, "skills": "Teamwork, Collaboration, Leadership, Customer Service, Professional Services, Regulatory Compliance, Technical Support, Product Management, Organizational Structure, Evaluation, Innovation, Software Development Methodology, Customer Experience, Software Development, Software Engineering, Training, Military, J2EE, JavaScript, JSON, Cascading Style Sheets, jQuery, Ajax, HTML5, React.js, AngularJS, Java, SOAP, RESTful, Web Services, Microservices, Spring Batch, Spring Framework, Database Administration, RDBMS, Microsoft SQL Server, Oracle, MySQL, JPA, Hibernate, CA Workload Automation AE, Git, GitHub, Agile, Automated Testing, Selenium, Work Ethic, Attention To Detail, ROOT, Conflict Resolution, Problem Solving, Life Insurance", "meta": {}}]