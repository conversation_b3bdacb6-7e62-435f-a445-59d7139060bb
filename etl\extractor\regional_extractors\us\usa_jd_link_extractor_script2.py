import re
import os
import csv
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
import argparse
import sys
import traceback
import logging
import asyncio

from config.core.settings import get_settings
from etl.loader.load_to_postgres import PostgresLoader

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- Configuration Loading ---
def load_configs_csv(filename="site-config-script(2).csv"):
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)
    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None or val.strip() == '': # Treat empty strings from CSV as default
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue
                
                for loc_key_base in ["pagination_locator", "iframe_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]

                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href'
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None
                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', 'none') or 'none'
                
                config_entry['next_button_disabled_attribute'] = get_safe_stripped_value(row, 'next_button_disabled_attribute') or None
                config_entry['next_button_disabled_value'] = get_safe_stripped_value(row, 'next_button_disabled_value') or None
                config_entry['next_button_text_check'] = get_safe_stripped_value(row, 'next_button_text_check') or None
                
                config_entry['headless'] = get_safe_stripped_value(row, 'headless', 'True').lower() == 'true'
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None  # Added
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None  # Added

                for time_key, default_val_s in [
                    ('initial_wait_time', 5), ('navigation_wait_time', 3),
                    ('page_load_timeout', 60), ('pagination_wait_time', 3),
                    ('max_pages', 50), ('max_clicks_for_load_more', 50)
                ]:
                    val_str = get_safe_stripped_value(row, time_key)
                    try:
                        config_entry[time_key] = int(val_str) if val_str else default_val_s
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{time_key}' in site '{company_name}'. Using default {default_val_s}.")
                        config_entry[time_key] = default_val_s
                
                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        raise
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        raise RuntimeError(f"Error loading CSV configurations: {e}")

# --- Helper Functions ---
def get_playwright_selector(locator_tuple):
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple}. Expected [strategy, value]. Using value as is.")
        return str(locator_tuple) 

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]: return f"css={value}"
    elif strategy_lower == "xpath": return f"xpath={value}"
    elif strategy_lower == "id": return f"id={value}" 
    elif strategy_lower == "name": return f"css=[name='{value}']" # Corrected for Playwright CSS
    elif strategy_lower in ["link_text", "text"]: return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]: return f"text*={value}"
    elif strategy_lower in ["tag_name", "tag"]: return f"css={value}" 
    elif strategy_lower in ["class", "class_name"]: return f"css=.{value.replace(' ', '.')}"
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}'. Defaulting to CSS: css={value}")
        return f"css={value}"

def resolve_url(base_url, link_url):
    if not link_url: return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'): return None
    
    if link_url.startswith("//"):
        parsed_base = urlparse(base_url if base_url else '') # Ensure base_url is a string
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"
    try:
        # If base_url is None and link_url is relative, urljoin would fail.
        # We rely on page.url being provided as base_url if config doesn't have one.
        return urljoin(base_url, link_url)
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Specific Handler Functions ---
async def async_scrape_fissionlabs_custom(page, current_scope, config):
    all_job_links_site = set()
    visited_pages_fission = set()
    
    logger.info("Executing FissionLabs custom pagination logic.")
    
    current_url = page.url
    visited_pages_fission.add(current_url)

    page_count = 0
    max_pages_fission = config.get("max_pages", 10)

    while page_count < max_pages_fission:
        page_count += 1
        logger.info(f"FissionLabs - Scraping page {page_count}: {page.url}")
        
        job_link_selector_str = get_playwright_selector(config["job_link_locator"])
        try:
            await current_scope.locator(job_link_selector_str).first.wait_for(state='attached', timeout=10000)
            job_elements = await current_scope.locator(job_link_selector_str).all()
            for element in job_elements:
                raw_link = await element.get_attribute("href")
                # Use page.url as fallback base_url
                absolute_link = resolve_url(config.get("base_url") or page.url, raw_link)
                if absolute_link and (not config.get("link_filter_keyword") or config.get("link_filter_keyword") in absolute_link):
                    all_job_links_site.add(absolute_link)
        except PlaywrightTimeoutError:
            logger.warning(f"FissionLabs - No job links found with selector '{job_link_selector_str}' on {page.url}.")
        except Exception as e:
            logger.error(f"FissionLabs - Error extracting links: {e}")

        found_next_page = False
        pagination_locator_str = get_playwright_selector(config["pagination_locator"])
        
        try:
            pagination_buttons = await current_scope.locator(pagination_locator_str).all()
            next_page_to_visit = None

            for btn in pagination_buttons:
                href_attr = await btn.get_attribute("href")
                if href_attr:
                    candidate_url = resolve_url(page.url, href_attr)
                    if candidate_url and candidate_url not in visited_pages_fission and candidate_url != page.url:
                       next_page_to_visit = candidate_url
                       break
            
            if next_page_to_visit:
                logger.info(f"FissionLabs - Navigating to next page: {next_page_to_visit}")
                await page.goto(next_page_to_visit, wait_until='domcontentloaded', timeout=config.get("page_load_timeout", 60) * 1000)
                await page.wait_for_timeout(config.get("pagination_wait_time", 3) * 1000)
                visited_pages_fission.add(page.url)
                found_next_page = True
            else:
                logger.info("FissionLabs - No new unvisited pagination links found.")
                break
        except PlaywrightTimeoutError:
            logger.warning(f"FissionLabs - Timeout looking for pagination buttons with '{pagination_locator_str}'.")
            break
        except Exception as e:
            logger.error(f"FissionLabs - Error during pagination: {e}")
            break
            
        if not found_next_page:
            break
            
    return all_job_links_site

# --- Core Asynchronous Scraping Function ---
async def scrape_job_links_async(site_name, configs):
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set()
    start_time = asyncio.get_event_loop().time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    async with async_playwright() as p:
        browser = None
        context = None
        page = None
        try:
            browser = await p.chromium.launch(headless=config.get('headless', True), args=['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'])
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                accept_downloads=False,
            )
            page = await context.new_page()
            current_scope = page # Default to page

            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info(f"Navigating to start URL: {config['start_url']}")
            wait_until_state = 'domcontentloaded'
            if site_name in ["Stripe", "AuthorityPartners", "Tebra"]:
                logger.info(f"Using 'networkidle' for initial page load for {site_name}")
                wait_until_state = 'networkidle'
            
            await page.goto(config['start_url'], wait_until=wait_until_state, timeout=default_timeout_ms)
            logger.info("Navigation successful.")

            if config.get("iframe_locator"):
                iframe_selector = get_playwright_selector(config["iframe_locator"])
                logger.info(f"Attempting to switch to iframe: {iframe_selector}")
                try:
                    frame_loc = page.frame_locator(iframe_selector)
                    # Make sure frame is usable, e.g. by waiting for its body
                    await frame_loc.locator('body').wait_for(state='visible', timeout=15000) 
                    current_scope = frame_loc
                    logger.info("Successfully focused on iframe.")
                except PlaywrightTimeoutError:
                    logger.error(f"Error: Timeout finding or confirming iframe {iframe_selector} for {site_name}.")
                    return set()


            initial_wait_s = config.get("initial_wait_time", 0)
            # Avoid double wait if networkidle was used and initial_wait_time is also set
            if initial_wait_s > 0 and wait_until_state != 'networkidle':
                logger.info(f"Performing initial wait of {initial_wait_s} seconds...")
                await page.wait_for_timeout(initial_wait_s * 1000)

            if config.get("pagination_type") == "custom_fissionlabs":
                all_job_links = await async_scrape_fissionlabs_custom(page, current_scope, config)
            else:
                current_page_num = 0
                max_pages = config.get("max_pages", 50)
                load_more_clicks = 0
                max_load_more = config.get("max_clicks_for_load_more", 50)
                consecutive_no_new_links = 0

                while current_page_num < max_pages:
                    current_page_num += 1
                    logger.info(f"Scraping page/view {current_page_num}...")
                    page_links_found_this_iteration = set()
                    
                    job_link_selector_str = get_playwright_selector(config["job_link_locator"])
                    wait_for_links_timeout_ms = 15 * 1000
                    
                    try:
                        # Determine effective scope for actions (page or frame)
                        action_scope = current_scope if hasattr(current_scope, 'locator') else page

                        if site_name == "NorthBay":
                            logger.info(f"NorthBay: Waiting for job links '{job_link_selector_str}' to be attached.")
                            await action_scope.locator(job_link_selector_str).first.wait_for(state='attached', timeout=wait_for_links_timeout_ms)
                        else:
                            await action_scope.locator(job_link_selector_str).first.wait_for(state='attached', timeout=wait_for_links_timeout_ms)

                        job_elements = await action_scope.locator(job_link_selector_str).all()
                        logger.info(f"Found {len(job_elements)} potential link elements on page {current_page_num}.")

                        for element in job_elements:
                            raw_link = None
                            extraction_method = config.get("link_extraction_method", "href")
                            
                            if extraction_method == "href":
                                raw_link = await element.get_attribute("href")
                            elif extraction_method == "attribute":
                                attr_name = config.get("link_attribute")
                                if attr_name:
                                    raw_link = await element.get_attribute(attr_name)
                                else:
                                    raw_link = await element.get_attribute("href") 
                            else:
                                 raw_link = await element.get_attribute("href")

                            # Fallback to page.url if config['base_url'] is not present
                            current_base_url = config.get("base_url") or page.url
                            absolute_link = resolve_url(current_base_url, raw_link)
                            
                            if absolute_link:
                                filter_kw = config.get("link_filter_keyword")
                                if filter_kw:
                                    if filter_kw.startswith("NOT:"):
                                        if filter_kw[4:] in absolute_link: continue
                                    elif filter_kw not in absolute_link: continue
                                page_links_found_this_iteration.add(absolute_link)
                    
                    except PlaywrightTimeoutError:
                        logger.warning(f"Timeout waiting for job links ('{job_link_selector_str}') on page {current_page_num}.")
                        if current_page_num == 1 and not all_job_links:
                             logger.warning(f"No job links found on the first page for {site_name}.")
                        if config.get("pagination_type") != "scroll_fully_then_extract":
                            break 
                    except PlaywrightError as e:
                        logger.error(f"Error locating/processing job links on page {current_page_num}: {e}")
                        break
                    
                    if not page_links_found_this_iteration and current_page_num > 1 and config.get("pagination_type") not in ["scroll_fully_then_extract", "none"]:
                        logger.info("No links extracted in this iteration. Assuming end of relevant pages.")
                        break

                    newly_added_count = len(page_links_found_this_iteration - all_job_links)
                    all_job_links.update(page_links_found_this_iteration)
                    logger.info(f"Found {len(page_links_found_this_iteration)} unique links on this view, {newly_added_count} new. Total: {len(all_job_links)}")

                    if newly_added_count == 0 and current_page_num > 1:
                        consecutive_no_new_links +=1
                        if consecutive_no_new_links >=2 and config.get("pagination_type") != "scroll_fully_then_extract":
                            logger.info(f"No new links for {consecutive_no_new_links} consecutive pages. Stopping pagination.")
                            break
                    else:
                        consecutive_no_new_links = 0

                    pagination_type = config.get("pagination_type", "none")
                    pagination_wait_s = config.get("pagination_wait_time", 3)
                    pagination_successful = False

                    if pagination_type == "none": break
                    
                    action_scope_for_pagination = current_scope if hasattr(current_scope, 'locator') else page
                    
                    if pagination_type == "scroll_fully_then_extract":
                        logger.info("Scrolling page to load all content...")
                        # Use current_scope for evaluate if scrolling within an iframe
                        scope_for_scroll = current_scope if hasattr(current_scope, 'evaluate') else page
                        last_height = await scope_for_scroll.evaluate("document.body.scrollHeight")
                        scroll_attempts = 0
                        while scroll_attempts < 15: # Max scroll attempts
                            await scope_for_scroll.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                            await page.wait_for_timeout(pagination_wait_s * 1000)
                            new_height = await scope_for_scroll.evaluate("document.body.scrollHeight")
                            if new_height == last_height:
                                logger.info("Scroll height did not change. Assuming all content loaded.")
                                break
                            last_height = new_height
                            scroll_attempts += 1
                        pagination_successful = True
                        break 

                    pag_locator_tuple = config.get("pagination_locator")
                    if not pag_locator_tuple and pagination_type not in ["none", "scroll_fully_then_extract"]:
                        logger.error(f"Pagination type '{pagination_type}' needs 'pagination_locator'. Stopping.")
                        break
                    
                    if pag_locator_tuple:
                        pag_selector_str = get_playwright_selector(pag_locator_tuple)
                        try:
                            pagination_element = action_scope_for_pagination.locator(pag_selector_str)
                            await pagination_element.wait_for(state='attached', timeout=10000)

                            if not await pagination_element.is_visible(timeout=5000):
                                logger.info("Pagination element found but not visible. End of pages.")
                                break
                            
                            if pagination_type == "next_button" or pagination_type == "next_button_custom_wait" or pagination_type == "next_button_url":
                                disabled_attr_cfg = config.get("next_button_disabled_attribute")
                                if disabled_attr_cfg:
                                    attr_val = await pagination_element.get_attribute(disabled_attr_cfg)
                                    disabled_val_cfg = config.get("next_button_disabled_value")
                                    if disabled_val_cfg:
                                        if attr_val == disabled_val_cfg:
                                            logger.info(f"Next button disabled ('{disabled_attr_cfg}' is '{attr_val}'). End of pages.")
                                            break
                                    elif attr_val is not None:
                                        logger.info(f"Next button disabled ('{disabled_attr_cfg}' attribute present). End of pages.")
                                        break
                                
                                text_check_cfg = config.get("next_button_text_check")
                                if text_check_cfg:
                                    btn_text = (await pagination_element.inner_text(timeout=2000)).strip()
                                    if btn_text != text_check_cfg:
                                        logger.info(f"Next button text '{btn_text}' != expected '{text_check_cfg}'. End of pages.")
                                        break
                            
                            if not await pagination_element.is_enabled(timeout=5000):
                                 logger.info("Pagination element found but not enabled. End of pages.")
                                 break

                            if pagination_type == "next_button_url":
                                next_url = await pagination_element.get_attribute('href')
                                resolved_next_url = resolve_url(page.url, next_url)
                                if resolved_next_url and resolved_next_url != page.url:
                                    logger.info(f"Navigating to next page URL: {resolved_next_url}")
                                    await page.goto(resolved_next_url, wait_until='domcontentloaded')
                                    pagination_successful = True
                                else: break
                            elif pagination_type in ["next_button", "load_more", "next_button_custom_wait"]:
                                logger.info(f"Clicking pagination element: {pag_selector_str}")
                                await pagination_element.click(timeout=10000)
                                pagination_successful = True
                                if pagination_type == "load_more":
                                    load_more_clicks += 1
                                    if load_more_clicks >= max_load_more:
                                        logger.info(f"Reached max clicks ({max_load_more}) for load_more. Stopping.")
                                        current_page_num = max_pages 
                            
                            if pagination_successful:
                                if pagination_type == "next_button_custom_wait":
                                    logger.info("Waiting for 'networkidle' after click...")
                                    await page.wait_for_load_state('networkidle', timeout=30000)
                                else:
                                    await page.wait_for_timeout(pagination_wait_s * 1000)
                            
                        except PlaywrightTimeoutError:
                            logger.warning(f"Pagination element '{pag_selector_str}' not found/interactable. End of pages.")
                            break
                        except PlaywrightError as pag_err:
                            logger.error(f"Error with pagination element '{pag_selector_str}': {pag_err}")
                            break
                    
                    if not pagination_successful and pagination_type != "none":
                        break
            # End of main scraping while loop
        except PlaywrightError as e:
            logger.error(f"A critical Playwright error occurred for {site_name}: {e}")
            logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(f"An unexpected error occurred during scraping for {site_name}: {e}")
            logger.error(traceback.format_exc())
        finally:
            if page and not page.is_closed(): await page.close()
            if context: await context.close()
            if browser and browser.is_connected(): await browser.close()

    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

# --- Main Asynchronous Execution ---
async def limited_scrape(semaphore, company_name, site_configs):
    async with semaphore:
        return await scrape_job_links_async(company_name, site_configs)

async def amain():
    settings = get_settings()
    db = PostgresLoader()
    db.connect()
    config_file = "usa_site_configs/site_config2.csv"
    sites = None  # if you wanna specific company
    output_file = f"{settings.LOCAL_SCRAPED_LINKS_DIR}/usa_job_links2.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    for i, company_name in enumerate(sites_to_scrape):
        result = results[i]  # Get the result for the current company
        config: dict = site_configs[company_name]  # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set()  # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")

        # Skip storing in the database if no links are found
        if not scraped_links:
            logger.info(f"No job links found for {company_name}. Skipping database storage.")
            continue

        # Check the company exists or create it
        company_id, created = db.get_or_create_company(company_name, config.get("company_url", ""), config)
        logger.info(f"Company ID: {company_id}, {'created' if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # It should be unique links
        links_to_process = db.process_new_links(links_to_process, company_id)

        # Skip storing in the database if no unique links are found after filtering
        if not links_to_process:
            logger.info(f"No unique job links to process for {company_name}. Skipping database storage.")
            continue

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': str(content_selectors_for_site) if content_selectors_for_site else None,
                })
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': '',
                })
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count

    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

async def main():
    await amain()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())