import re
import os
import csv
from urllib.parse import urljoin, urlparse
from etl.extractor.models.jd_schema import ScrappedJ<PERSON><PERSON>inkModel
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
import sys
import traceback
import logging
import asyncio
import json # For ProgressiveByte
import html

from config.core.settings import get_settings
from etl.loader.load_to_postgres import PostgresLoader # For ProgressiveByte

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- Configuration Loading (Reads from CSV) ---
def load_configs_csv(filename: str):
    """Loads site configurations from a CSV file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None:
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue

                for loc_key_base in ["initial_wait_locator", "navigation_click_locator", "pagination_locator", "iframe_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]

                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', 'none') or 'none'
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href'
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['onclick_regex'] = get_safe_stripped_value(row, 'onclick_regex') or None
                config_entry['pagination_locator_template'] = get_safe_stripped_value(row, 'pagination_locator_template') or None
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None
                config_entry['items_per_page_for_url_iteration'] = get_safe_stripped_value(row, 'items_per_page_for_url_iteration')

                for int_key, default_val in [
                    ('initial_wait_time', 5), ('initial_wait_timeout', 20),
                    ('navigation_wait_time', 5), ('page_load_timeout', 60),
                    ('pagination_wait_time', 5), ('max_pages', 50)
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val
                
                items_per_page_str = config_entry.get('items_per_page_for_url_iteration')
                try:
                    config_entry['items_per_page_for_url_iteration'] = int(items_per_page_str) if items_per_page_str else 25
                except ValueError:
                    logger.warning(f"Invalid integer value '{items_per_page_str}' for 'items_per_page_for_url_iteration' in site '{company_name}'. Using default 25.")
                    config_entry['items_per_page_for_url_iteration'] = 25


                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        raise
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        raise RuntimeError(f"Error loading CSV configurations: {e}")

# --- Helper Functions (Remain Synchronous - same as before) ---
def get_playwright_selector(locator_tuple):
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple}. Expected [strategy, value]. Using value as is.")
        return str(locator_tuple)

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]:
        return f"css={value}"
    elif strategy_lower == "xpath":
        if not (value.startswith('/') or value.startswith('(') or value.startswith('.')):
             logger.warning(f"XPath '{value}' might be invalid (doesn't start with /, (, or .). Using it anyway.")
        return f"xpath={value}"
    elif strategy_lower == "id":
        return f"#{value}"
    elif strategy_lower == "name":
         return f"[name='{value}']"
    elif strategy_lower in ["link_text", "text"]:
         return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]:
         return f"text*={value}"
    elif strategy_lower in ["tag_name", "tag"]:
         return f"{value}"
    elif strategy_lower in ["class", "class_name"]:
        return f".{value.replace(' ', '.')}"
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}' for Playwright conversion. Using value directly as CSS selector: {value}")
        return f"css={value}"

def resolve_url(base_url, link_url):
    if not link_url:
        return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'):
        return None

    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"
    try:
        absolute_url = urljoin(base_url, link_url)
        if urlparse(absolute_url).scheme in ['http', 'https'] and urlparse(absolute_url).netloc:
            return absolute_url
        else:
            logger.warning(f"Resolved URL '{absolute_url}' from base '{base_url}' and link '{link_url}' seems invalid. Skipping.")
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Core Asynchronous Scraping Function (with minor additions for new link extraction methods) ---
async def scrape_job_links_async(site_name, configs):
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set()
    start_time = asyncio.get_event_loop().time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    browser = None
    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True, args=["--no-sandbox", "--disable-dev-shm-usage"])
        except PlaywrightError as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            return set()

        context = None
        page = None
        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                accept_downloads=False,
                # ignore_https_errors=True # Uncomment if dealing with many SSL cert errors (use with caution)
            )
            # try:
            #     await context.grant_permissions(['geolocation', 'notifications'], origin=config['start_url'])
            # except Exception as perm_err:
            #     logger.warning(f"Could not grant permissions for {config['start_url']}: {perm_err}")


            page = await context.new_page()
            current_scope = page

            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            MAX_GOTO_RETRIES = 3
            goto_success = False
            initial_url_to_load = config['start_url']
            perform_initial_goto = True

            if config.get("pagination_type") == "url_iteration":
                page_param_template_check = config.get('pagination_locator_template', "")
                if ("{page_num}" in page_param_template_check or "{startrow}" in page_param_template_check):
                    items_per_page_check = config.get('items_per_page_for_url_iteration', 25)
                    start_url_base_check = config['start_url']
                    if (page_param_template_check.startswith('/') or page_param_template_check.startswith('?')) and start_url_base_check.endswith('/'):
                        start_url_base_check = start_url_base_check.rstrip('/')
                    
                    first_iter_url_check = ""
                    if "{startrow}" in page_param_template_check:
                        first_iter_url_check = f"{start_url_base_check}{page_param_template_check.format(startrow=0)}"
                    elif "{page_num}" in page_param_template_check:
                         first_iter_url_check = f"{start_url_base_check}{page_param_template_check.format(page_num=1)}"
                    
                    # If start_url is just a base and the template constructs the first actual page URL that's different
                    if first_iter_url_check and first_iter_url_check != config['start_url']:
                         perform_initial_goto = False
                         logger.info(f"URL iteration: Initial navigation for {site_name} will be handled by the pagination loop, starting with what would be: {first_iter_url_check}")
                    # If start_url already IS the first page of an iteration (e.g. contains ?startrow=0), then do initial goto
                    elif first_iter_url_check and first_iter_url_check == config['start_url']:
                        perform_initial_goto = True
                    # If no {page_num} or {startrow} in template, start_url is the only URL.
                    elif not first_iter_url_check and page_param_template_check: # Template exists but no iter vars
                        perform_initial_goto = True
                    elif not page_param_template_check: # No template at all
                        perform_initial_goto = True


            if perform_initial_goto:
                for attempt in range(MAX_GOTO_RETRIES):
                    try:
                        logger.info(f"Navigating to start URL (Attempt {attempt + 1}/{MAX_GOTO_RETRIES}): {initial_url_to_load}")
                        response = await page.goto(initial_url_to_load, wait_until='domcontentloaded', timeout=default_timeout_ms)
                        if response and response.ok:
                            logger.info("Initial navigation successful.")
                            goto_success = True
                            break
                        elif response:
                            logger.warning(f"Received non-OK status code {response.status} for {initial_url_to_load} on attempt {attempt + 1}.")
                        else:
                            logger.warning(f"No response received for {initial_url_to_load} on attempt {attempt + 1}.")
                        if attempt < MAX_GOTO_RETRIES - 1:
                            wait_seconds = 3 * (attempt + 1)
                            logger.info(f"Waiting {wait_seconds}s before retry...")
                            await asyncio.sleep(wait_seconds)
                    except PlaywrightTimeoutError:
                        logger.error(f"Timeout navigating to {initial_url_to_load} on attempt {attempt + 1}.")
                        if attempt < MAX_GOTO_RETRIES - 1: await asyncio.sleep(3 * (attempt+1))
                    except PlaywrightError as e:
                        logger.error(f"Playwright navigation error for {site_name} to {initial_url_to_load} on attempt {attempt + 1}: {e}")
                        if attempt < MAX_GOTO_RETRIES - 1: await asyncio.sleep(3 * (attempt+1))
                
                if not goto_success:
                    logger.error(f"Failed to navigate to {initial_url_to_load} after {MAX_GOTO_RETRIES} attempts for {site_name}.")
                    if browser: await browser.close(); return set()
            else: # If not performing initial goto, it means url_iteration will handle the first navigation.
                goto_success = True # Mark as success to proceed, actual navigation in loop

            if not goto_success: # Should ideally not be reached if logic is correct
                 if browser: await browser.close(); return set()


            if config.get("iframe_locator"):
                iframe_selector = get_playwright_selector(config["iframe_locator"])
                logger.info(f"Attempting to switch to iframe: {iframe_selector}")
                try:
                    await page.wait_for_selector(iframe_selector, state='attached', timeout=config.get("initial_wait_timeout", 20) * 1000)
                    frame_locator = page.frame_locator(iframe_selector)
                    await frame_locator.locator(':root').wait_for(state='attached', timeout=config.get("initial_wait_timeout", 20) * 1000)
                    current_scope = frame_locator
                    logger.info("Successfully focused on iframe.")
                except PlaywrightTimeoutError:
                    logger.error(f"Timeout waiting for iframe ({iframe_selector}) or its content for {site_name}.")
                    if browser: await browser.close(); return set()
                except PlaywrightError as e:
                    logger.error(f"Error locating iframe {iframe_selector} for {site_name}: {e}")
                    if browser: await browser.close(); return set()

            nav_clicked = False
            if config.get("navigation_click_locator") and config.get("pagination_type") == "click_to_navigate":
                nav_selector = get_playwright_selector(config["navigation_click_locator"])
                initial_wait_timeout_ms_nav = config.get("initial_wait_timeout", 20) * 1000
                nav_wait_ms = config.get("navigation_wait_time", 5) * 1000
                try:
                    logger.info(f"Attempting initial navigation click: {nav_selector}")
                    nav_button = current_scope.locator(nav_selector)
                    await nav_button.wait_for(state="visible", timeout=initial_wait_timeout_ms_nav)
                    await nav_button.click(timeout=initial_wait_timeout_ms_nav // 2)
                    logger.info(f"Navigation element clicked. Waiting {nav_wait_ms / 1000}s...")
                    await page.wait_for_timeout(nav_wait_ms)
                    nav_clicked = True
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding or clicking initial navigation element {nav_selector} for {site_name}.")
                except PlaywrightError as e:
                    logger.error(f"Error during initial navigation click for {site_name}: {e}")
                    if browser: await browser.close(); return set()

            if config.get("initial_wait_locator") and not nav_clicked:
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                initial_wait_timeout_ms_loc = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {initial_wait_timeout_ms_loc / 1000}s)")
                    await current_scope.locator(wait_selector).first.wait_for(state='attached', timeout=initial_wait_timeout_ms_loc)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial wait element {wait_selector} not found within timeout for {site_name}. Continuing...")
                except PlaywrightError as e:
                     logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")
            elif not (config.get("navigation_click_locator") and nav_clicked) and not config.get("initial_wait_locator"):
                 initial_wait_ms_fallback = config.get("initial_wait_time", 5) * 1000
                 if initial_wait_ms_fallback > 0:
                     logger.info(f"Performing initial generic wait of {initial_wait_ms_fallback / 1000} seconds...")
                     await page.wait_for_timeout(initial_wait_ms_fallback)

            current_page_num = 1
            max_pages = config.get("max_pages", 50)
            consecutive_no_new_links = 0

            if config.get("pagination_type") == "url_iteration":
                logger.info(f"Starting URL iteration logic for {site_name}")
                start_url_base = config['start_url']
                page_param_template = config.get('pagination_locator_template', "")
                if (page_param_template.startswith('/') or page_param_template.startswith('?')) and start_url_base.endswith('/'):
                    start_url_base = start_url_base.rstrip('/')
                items_per_page = config.get('items_per_page_for_url_iteration', 25)

                for page_iter in range(max_pages):
                    page_links_found_iter = set()
                    current_iter_url = ""
                    
                    if "{startrow}" in page_param_template:
                        param_val = page_iter * items_per_page
                        current_iter_url = f"{start_url_base}{page_param_template.format(startrow=param_val)}"
                    elif "{page_num}" in page_param_template:
                        param_val = page_iter + 1
                        current_iter_url = f"{start_url_base}{page_param_template.format(page_num=param_val)}"
                    else:
                        current_iter_url = start_url_base
                        if page_iter > 0: logger.info("URL iteration: No template variables, already processed. Stopping."); break
                        logger.info(f"URL iteration: No template variables, processing start_url once: {current_iter_url}")

                    # Navigation for URL iteration:
                    # if perform_initial_goto was True, the first page (page_iter == 0 AND current_iter_url == initial_url_to_load) is already loaded.
                    # Otherwise, or for subsequent pages, navigate.
                    if not (perform_initial_goto and page_iter == 0 and current_iter_url == initial_url_to_load):
                        logger.info(f"URL Iteration - Page {page_iter + 1}: Navigating to {current_iter_url}")
                        nav_success_iter = False
                        for nav_attempt_iter in range(MAX_GOTO_RETRIES): # Use MAX_GOTO_RETRIES
                            try:
                                response_iter = await page.goto(current_iter_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                if response_iter and response_iter.ok:
                                    nav_success_iter = True; break
                                elif response_iter: logger.warning(f"URL Iteration: Non-OK status {response_iter.status} for {current_iter_url} (Attempt {nav_attempt_iter+1})")
                                else: logger.warning(f"URL Iteration: No response for {current_iter_url} (Attempt {nav_attempt_iter+1})")
                                if nav_attempt_iter < MAX_GOTO_RETRIES - 1: await asyncio.sleep(3 * (nav_attempt_iter + 1))
                            except PlaywrightTimeoutError:
                                logger.warning(f"URL Iteration: Timeout for {current_iter_url} (Attempt {nav_attempt_iter+1})")
                                if nav_attempt_iter < MAX_GOTO_RETRIES - 1: await asyncio.sleep(3 * (nav_attempt_iter + 1))
                            except PlaywrightError as e_iter_nav:
                                logger.error(f"URL Iteration: Navigation error for {current_iter_url} (Attempt {nav_attempt_iter+1}): {e_iter_nav}")
                                if nav_attempt_iter < MAX_GOTO_RETRIES - 1: await asyncio.sleep(3 * (nav_attempt_iter + 1))
                        if not nav_success_iter: logger.error(f"URL Iteration: Failed to load {current_iter_url}. Stopping."); break
                    elif page_iter == 0: # First iteration, and it was already loaded by initial goto
                         logger.info(f"URL Iteration - Page {page_iter + 1}: Using pre-loaded page: {current_iter_url}")


                    await page.wait_for_timeout(config.get("pagination_wait_time", 3) * 1000)
                    
                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    try:
                        await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                        job_elements = await current_scope.locator(job_link_selector).all()
                        logger.info(f"URL Iteration: Found {len(job_elements)} potential links on page {page_iter + 1}.")
                        if not job_elements and page_iter > 0: # If not the first page and no elements, likely end
                            logger.info(f"URL Iteration: No job elements found on page {page_iter + 1}. Assuming end."); break

                        for element in job_elements:
                            raw_link = await element.get_attribute("href")
                            absolute_link = resolve_url(config.get("base_url") or current_iter_url, raw_link)
                            if absolute_link:
                                link_filter = config.get("link_filter_keyword")
                                if link_filter:
                                    if link_filter.startswith("NOT:"):
                                        if link_filter[4:] in absolute_link: continue
                                    elif link_filter not in absolute_link: continue
                                page_links_found_iter.add(absolute_link)
                    except PlaywrightTimeoutError:
                        logger.warning(f"URL Iteration: No job links ({job_link_selector}) on {current_iter_url}. Assuming end if not first page.")
                        if page_iter > 0: break
                    except PlaywrightError as e_extract:
                        logger.error(f"URL Iteration: Error locating links on {current_iter_url}: {e_extract}"); break
                    
                    if not page_links_found_iter and page_iter > 0 :
                         logger.info(f"URL Iteration: No new links extracted from {current_iter_url} (page {page_iter+1}). Assuming end."); break
                    
                    newly_added_count_iter = len(page_links_found_iter - all_job_links)
                    if newly_added_count_iter == 0 and page_iter > 0 and len(page_links_found_iter) > 0:
                        consecutive_no_new_links +=1
                        logger.info(f"URL iteration page {page_iter+1}: No new links added to global set. Consecutive: {consecutive_no_new_links}")
                        if consecutive_no_new_links >= (config.get("consecutive_empty_pages_threshold",2) if config.get("consecutive_empty_pages_threshold") else 2) : # Allow configuring threshold
                            logger.info(f"URL iteration: Stopping after {consecutive_no_new_links} consecutive pages with no new global links.")
                            break
                    else:
                        consecutive_no_new_links = 0
                    all_job_links.update(page_links_found_iter)

            else: # Other pagination types
                while current_page_num <= max_pages:
                    logger.info(f"Scraping page/view {current_page_num} for {site_name}...")
                    page_links_found_this_view = set()
                    await page.wait_for_timeout(config.get("pagination_wait_time",2)*1000) 

                    try:
                        job_link_selector = get_playwright_selector(config["job_link_locator"])
                        link_extraction_method = config.get("link_extraction_method", "href")
                        link_attribute = config.get("link_attribute")
                        onclick_regex_str = config.get("onclick_regex")
                        link_filter_keyword = config.get("link_filter_keyword")
                        base_url_config = config.get("base_url")

                        onclick_regex_compiled = None
                        if link_extraction_method == "onclick_regex" and onclick_regex_str:
                            try: onclick_regex_compiled = re.compile(onclick_regex_str)
                            except re.error as re_err: logger.warning(f"Invalid onclick_regex. Falling back to 'href'. Error: {re_err}"); link_extraction_method = "href"
                        
                        job_elements = []
                        try:
                            await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                            job_elements = await current_scope.locator(job_link_selector).all()
                            logger.info(f"Found {len(job_elements)} potential links on page/view {current_page_num}.")
                        except PlaywrightTimeoutError:
                            logger.warning(f"Timeout waiting for job links ({job_link_selector}) on page/view {current_page_num}.")
                            if current_page_num == 1 and not all_job_links: logger.error(f"No job links found on first page for {site_name}. Check locators/waits.")
                            if config.get("pagination_type") not in ['scroll', 'load_more_scroll', 'next_button_js_scroll']: break 
                        except PlaywrightError as e_loc: logger.error(f"Error locating job links ({job_link_selector}): {e_loc}"); break
                        
                        if not job_elements and config.get("pagination_type") not in ['scroll', 'load_more_scroll', 'next_button_js_scroll']:
                             if current_page_num > 1: logger.info("No more job link elements after pagination action. Ending.")
                             elif not all_job_links: logger.warning(f"No job elements on initial page for {site_name} ({job_link_selector}).")
                             break

                        for i, element in enumerate(job_elements):
                            raw_link = None
                            current_element_base_url = base_url_config or page.url
                            try:
                                if link_extraction_method == "href":
                                    raw_link = await element.get_attribute("href")
                                elif link_extraction_method == "attribute" and link_attribute:
                                    raw_link = await element.get_attribute(link_attribute)
                                elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                                    onclick_attr = await element.get_attribute("onclick")
                                    if onclick_attr:
                                        match = onclick_regex_compiled.search(onclick_attr)
                                        if match and len(match.groups()) > 0: raw_link = match.group(1)
                                        elif match : logger.warning(f"onclick_regex matched but no capturing group for site {site_name}")
                                elif link_extraction_method == "amicus_id_constructor":
                                    job_id = await element.get_attribute("id");
                                    if job_id and base_url_config: raw_link = f"{base_url_config.rstrip('/')}/{job_id}" # Ensure one slash
                                elif link_extraction_method == "span_id_constructor":
                                    job_id = await element.get_attribute("data-jobid")
                                    if job_id and base_url_config: raw_link = f"{base_url_config.rstrip('/')}/{job_id}"
                                elif link_extraction_method == "zs_custom_url_constructor":
                                    href_attr = await element.get_attribute("href")
                                    if href_attr and "/all/jobs/" in href_attr and base_url_config:
                                        try: job_id_part = href_attr.split("/all/jobs/")[1].split("?")[0].strip("/"); raw_link = f"{base_url_config.rstrip('/')}/all/jobs/{job_id_part}?lang=en-us"
                                        except IndexError: logger.warning(f"Could not parse job ID from href for ZS Associates: {href_attr}")
                                elif link_extraction_method == "data_eael_wrapper_link_json_url":
                                    data_attr = await element.get_attribute(link_attribute or "data-eael-wrapper-link")
                                    if data_attr:
                                        try: decoded_data = html.unescape(data_attr); link_data = json.loads(decoded_data); raw_link = link_data.get("url")
                                        except (json.JSONDecodeError, TypeError) as json_err: logger.warning(f"JSON parse error for ProgressiveByte: {json_err}, data: {data_attr[:100]}")
                                else: # Fallback
                                    raw_link = await element.get_attribute("href")
                                    if link_extraction_method not in ["href", "attribute", "onclick_regex", "amicus_id_constructor", "span_id_constructor", "zs_custom_url_constructor", "data_eael_wrapper_link_json_url"]:
                                         logger.warning(f"Unknown link_extraction_method '{link_extraction_method}', defaulted to 'href'.")

                                absolute_link = resolve_url(current_element_base_url, raw_link)
                                if absolute_link:
                                    if link_filter_keyword:
                                        if link_filter_keyword.startswith("NOT:"):
                                            if link_filter_keyword[4:] in absolute_link: continue
                                        elif link_filter_keyword not in absolute_link: continue
                                    if absolute_link.rstrip('/') == config['start_url'].rstrip('/'):
                                        jl_s, jl_v = config["job_link_locator"]
                                        if jl_s.lower() in ["css", "tag_name", "xpath"] and jl_v == "a": continue # Avoid start_url if very generic selector
                                    page_links_found_this_view.add(absolute_link)
                            except PlaywrightError as el_err: logger.error(f"Error processing element {i+1} on page/view {current_page_num}: {el_err}")
                            except Exception as e_el: logger.error(f"Unexpected error on element {i+1}: {e_el}")
                    
                    except Exception as page_err_extract: logger.error(f"Error during link extraction on page/view {current_page_num}: {page_err_extract}"); logger.error(traceback.format_exc()); break

                    newly_added_to_global = len(page_links_found_this_view - all_job_links)
                    logger.info(f"Found {len(page_links_found_this_view)} links on this view, {newly_added_to_global} are new globally.")
                    
                    pagination_type = config.get("pagination_type", "none")
                    # Check for stop condition based on newly_added_to_global for all types
                    if newly_added_to_global == 0 and current_page_num > 1 and len(page_links_found_this_view) > 0:
                        consecutive_no_new_links += 1
                        logger.info(f"No new global links after {pagination_type} on page/view {current_page_num}. Consecutive: {consecutive_no_new_links}")
                        if consecutive_no_new_links >= (config.get("consecutive_empty_pages_threshold",2) if config.get("consecutive_empty_pages_threshold") else 2):
                            logger.info(f"Stopping pagination after {consecutive_no_new_links} views with no new global links.")
                            break
                    elif newly_added_to_global > 0:
                        consecutive_no_new_links = 0
                    elif len(page_links_found_this_view) == 0 and current_page_num > 1 : # No links found on this view after first page
                        logger.info(f"Found 0 links on page/view {current_page_num} after {pagination_type}. Stopping pagination.")
                        break
                    
                    all_job_links.update(page_links_found_this_view) # Add to global set

                    pagination_successful = False
                    pagination_wait_ms_action = config.get("pagination_wait_time", 5) * 1000
                    if pagination_type == "none" or (pagination_type == "click_to_navigate" and nav_clicked): logger.info("Pagination type is 'none' or 'click_to_navigate' (already done). Stopping."); break
                    
                    elif pagination_type in ["scroll", "load_more_scroll", "next_button_js_scroll"]:
                        logger.info(f"Pagination type '{pagination_type}'. Scrolling for page {current_page_num + 1}...")
                        scope_scroll = current_scope if hasattr(current_scope, 'evaluate') else page
                        last_h = await scope_scroll.evaluate("document.body.scrollHeight")
                        await scope_scroll.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                        await page.wait_for_timeout(pagination_wait_ms_action + 1000)
                        new_h = await scope_scroll.evaluate("document.body.scrollHeight")
                        clicked_in_scroll = False
                        if pagination_type != "scroll": # i.e. load_more_scroll or next_button_js_scroll
                            pag_loc_scroll = config.get("pagination_locator")
                            if pag_loc_scroll:
                                final_pag_loc_scroll = get_playwright_selector(pag_loc_scroll)
                                try:
                                    pag_el_scroll = current_scope.locator(final_pag_loc_scroll).first
                                    is_vis_scroll = await pag_el_scroll.is_visible(timeout=2000)
                                    is_dis_scroll = await pag_el_scroll.is_disabled(timeout=2000) or \
                                                    (await pag_el_scroll.get_attribute("aria-disabled") == "true") or \
                                                    await pag_el_scroll.evaluate("el => el.hasAttribute('disabled') or el.classList.contains('Mui-disabled') or el.classList.contains('mat-button-disabled')")
                                    if is_vis_scroll and not is_dis_scroll:
                                        logger.info(f"Clicking button for {pagination_type}: {final_pag_loc_scroll}")
                                        await pag_el_scroll.click(timeout=3000)
                                        await page.wait_for_timeout(pagination_wait_ms_action)
                                        clicked_in_scroll = True
                                    else: logger.info(f"Button for {pagination_type} ({final_pag_loc_scroll}) not interactable after scroll.")
                                except PlaywrightTimeoutError: logger.info(f"Button for {pagination_type} ({final_pag_loc_scroll}) not found after scroll.")
                                except PlaywrightError as e_click_s: logger.warning(f"Error clicking for {pagination_type} after scroll: {e_click_s}")
                        if new_h == last_h and not clicked_in_scroll and current_page_num > 1: logger.info("Scroll height unchanged and no button click for scroll type. Assuming end."); break
                        pagination_successful = True
                    
                    elif pagination_type in ["next_button", "load_more", "next_button_js", "load_more_js", "next_button_url", "next_button_data_table"]:
                        pag_loc_btn = config.get("pagination_locator")
                        pag_tpl_btn = config.get("pagination_locator_template")
                        final_pag_loc_btn = None
                        if pag_loc_btn: final_pag_loc_btn = get_playwright_selector(pag_loc_btn)
                        elif pagination_type == "next_button_data_table" and pag_tpl_btn:
                            try: final_pag_loc_btn = get_playwright_selector(['xpath', pag_tpl_btn.format(page_num=(current_page_num + 1))])
                            except Exception as fmt_e: logger.error(f"Error formatting template for {pagination_type}: {fmt_e}"); break
                        if not final_pag_loc_btn: logger.error(f"{pagination_type} needs 'pagination_locator' or valid 'template'. Stopping."); break
                        
                        try:
                            logger.info(f"Looking for {pagination_type} element for page {current_page_num + 1}: {final_pag_loc_btn}")
                            pag_el_btn = current_scope.locator(final_pag_loc_btn).first
                            await pag_el_btn.wait_for(state='attached', timeout=10000)
                            if not await pag_el_btn.is_visible(timeout=5000): logger.info("Button element not visible. Assuming end."); break
                            is_btn_disabled_action = await pag_el_btn.is_disabled(timeout=3000) or \
                                                    (await pag_el_btn.get_attribute("aria-disabled") == "true") or \
                                                    await pag_el_btn.evaluate("el => el.hasAttribute('disabled') or el.classList.contains('Mui-disabled') or el.classList.contains('mat-button-disabled')")
                            if is_btn_disabled_action: logger.info("Button element is disabled. Assuming end."); break
                            
                            if pagination_type == "next_button_url":
                                 next_url_val = await pag_el_btn.get_attribute('href')
                                 resolved_next_url_val = resolve_url(page.url, next_url_val)
                                 if resolved_next_url_val and resolved_next_url_val != page.url and "javascript:void(0)" not in resolved_next_url_val.lower():
                                     logger.info(f"Navigating to next URL: {resolved_next_url_val}")
                                     await page.goto(resolved_next_url_val, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                     pagination_successful = True
                                 else: logger.info("Next button (URL) no valid/different URL. Assuming end."); break
                            else:
                                logger.info(f"Clicking {pagination_type} element: {final_pag_loc_btn}")
                                await pag_el_btn.scroll_into_view_if_needed(timeout=5000)
                                if "js" in pagination_type: await current_scope.evaluate("arguments[0].click();", await pag_el_btn.element_handle())
                                else: await pag_el_btn.click(timeout=10000)
                                logger.info(f"Clicked. Waiting {pagination_wait_ms_action / 1000}s...")
                                await page.wait_for_timeout(pagination_wait_ms_action)
                                if 'js' in pagination_type or pagination_type == "load_more": await page.wait_for_load_state('networkidle', timeout=20000)
                                pagination_successful = True
                        except PlaywrightTimeoutError: logger.info(f"{pagination_type} element '{final_pag_loc_btn}' not found/interactable. Assuming end."); break
                        except PlaywrightError as pag_e_btn: logger.error(f"Error with {pagination_type} element '{final_pag_loc_btn}': {pag_e_btn}"); break
                        except Exception as e_glob_btn: logger.error(f"Unexpected error during {pagination_type}: {e_glob_btn}"); break
                    else: logger.error(f"Unknown pagination_type '{pagination_type}'. Stopping."); break

                    if not pagination_successful : logger.info(f"Pagination action for '{pagination_type}' failed. Stopping."); break
                    current_page_num += 1
            
            if current_page_num > max_pages and config.get("pagination_type") != "url_iteration":
                logger.info(f"Reached max pages ({max_pages}) for {site_name}.")

        except PlaywrightError as e_outer:
            logger.error(f"A critical Playwright error occurred for {site_name}: {e_outer}")
            logger.error(traceback.format_exc())
        except Exception as e_gen_outer:
            logger.error(f"An unexpected error occurred during scraping for {site_name}: {e_gen_outer}")
            logger.error(traceback.format_exc())
        finally:
            if context: await context.close()
            if browser: await browser.close()
    
    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "new_usa_site_configs/New_USA_March22_Script(2).csv"
    sites = None  # if you wanna specific company
    output_file = f"{get_settings().LOCAL_SCRAPED_LINKS_DIR}/results_for_script4.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i] # Get the result for the current company
        config: dict = site_configs[company_name] # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set() # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")
        
        # check the company exists or create it 
        company_id, created = db.get_or_create_company(company_name, config.get("company_url",""), config)
        logger.info(f"Company ID: {company_id}, {"created" if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # it should be unique links
        links_to_process = db.process_new_links(links_to_process,company_id)

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        links_to_process = links_to_process if links_to_process else set()
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                                        job_url=link,
                                        content_selectors=str(content_selectors_for_site) if content_selectors_for_site else None,
                                        ).model_dump()
                )
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append(
                    ScrappedJDLinkModel(company_id=company_id,
                        job_url=link).model_dump())
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors','source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

async def main():
    await amain()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())