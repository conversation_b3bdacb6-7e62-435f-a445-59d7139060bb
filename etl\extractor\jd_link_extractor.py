# import asyncio
from etl.extractor import logger
from etl.extractor.regional_extractors.uk.career_page_uk_extractor import main as uk_career_page_extractor
from etl.extractor.regional_extractors.new_usa.usa_career_page_extractor import main as usa_career_page_extractor
from etl.extractor.regional_extractors.us.usa_career_page_extractor import main as usa_career_page_extractor1
from etl.extractor.job_provider.dice_jd_link_extractor import main as dice_extractor

import asyncio

async def main():
    logger.info("Starting job link extraction (async)")

    # Run all extractors concurrently
    tasks = [
        asyncio.create_task(run_dice_extractor()),
        asyncio.create_task(run_uk_career_page_extractor()),
        asyncio.create_task(run_usa_career_page_extractor()),
        asyncio.create_task(run_usa_career_page_extractor1())
    ]

    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Log results
    extractors = ["Dice", "UK Career Page", "USA Career Page", "USA Career Page 1"]
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"{extractors[i]} extractor failed: {result}")
        else:
            logger.info(f"{extractors[i]} extractor completed successfully")

    logger.info("All job link extractors completed!")

async def run_dice_extractor():
    """Async wrapper for dice_extractor"""
    logger.info("Starting Dice extraction")
    await dice_extractor()
    logger.info("Dice extraction completed")

async def run_uk_career_page_extractor():
    """Async wrapper for uk_career_page_extractor"""
    logger.info("Starting UK career page extraction")
    await uk_career_page_extractor()
    logger.info("UK career page extraction completed")

async def run_usa_career_page_extractor():
    """Async wrapper for usa_career_page_extractor"""
    logger.info("Starting USA career page extraction")
    await usa_career_page_extractor()
    logger.info("USA career page extraction completed")

async def run_usa_career_page_extractor1():
    """Async wrapper for usa_career_page_extractor1"""
    logger.info("Starting USA career page extraction 1")
    await usa_career_page_extractor1()
    logger.info("USA career page extraction 1 completed")

if __name__ == "__main__":
    asyncio.run(main())