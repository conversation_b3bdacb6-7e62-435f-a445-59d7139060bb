from .new_usa_jd_link_extractor_script import main as script1
from .new_usa_jd_link_extractor_script2 import main as script2
from .new_usa_jd_link_extractor_script3 import main as script3
from .new_usa_jd_link_extractor_script4 import main as script4
from .new_usa_jd_link_extractor_script5 import main as script5
from .new_usa_jd_link_extractor_script6 import main as script6
from .new_usa_jd_link_extractor_script7 import main as script7
from .new_usa_jd_link_extractor_script8 import main as script8
from .new_usa_jd_link_extractor_script9 import main as script9
from .new_usa_jd_link_extractor_script10 import main as script10


async def main():
    # Running all scripts with await
    await script1()
    await script2()
    await script3()
    await script4()
    await script5()
    await script6()
    await script7()
    await script8()
    await script9()
    await script10()