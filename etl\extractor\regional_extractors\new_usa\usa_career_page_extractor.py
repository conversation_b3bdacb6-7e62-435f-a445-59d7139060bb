from .new_usa_jd_link_extractor_script import main as script1
from .new_usa_jd_link_extractor_script2 import main as script2
from .new_usa_jd_link_extractor_script3 import main as script3
from .new_usa_jd_link_extractor_script4 import main as script4
from .new_usa_jd_link_extractor_script5 import main as script5
from .new_usa_jd_link_extractor_script6 import main as script6
from .new_usa_jd_link_extractor_script7 import main as script7
from .new_usa_jd_link_extractor_script8 import main as script8
from .new_usa_jd_link_extractor_script9 import main as script9
from .new_usa_jd_link_extractor_script10 import main as script10


def main():
    # For testing - only run first script instead of all 10
    script1()
    # script2()
    # script3()
    # script4()
    # script5()
    # script6()
    # script7()
    # script8()
    # script9()
    # script10()