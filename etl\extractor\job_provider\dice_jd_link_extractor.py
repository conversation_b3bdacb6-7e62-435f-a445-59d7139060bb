import json
from etl.extractor.models.enum import JobSource
from etl.loader.load_to_postgres import PostgresLoader
import asyncio
from playwright.async_api import async_playwright
import os
import requests
import csv
import logging
from collections import defaultdict
from dotenv import load_dotenv
from config.core.settings import get_settings
load_dotenv()

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class DiceJobLinkExtractor:
    def __init__(self, api_key, output_csv, db: PostgresLoader, country_code, location, max_jobs=1000, page_size=100):
        self.api_key = api_key
        self.output_csv = output_csv
        self.max_jobs = max_jobs
        self.page_size = page_size
        self.db = db
        self.country_code = country_code
        self.location = location
        self.headers = {
            "x-api-key": self.api_key,
            "Accept": "application/json"
        }


    def fetch_jobs(self, page=1):
        url = "https://job-search-api.svc.dhigroupinc.com/v1/dice/jobs/search?"
        params = {
            "countryCode": self.country_code,
            "page": page,
            "pageSize": self.page_size,
            "location": self.location,
            "filters.postedDate": "O" # latest job
        }
        try:
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"Failed to fetch jobs on page {page}: {e}")
            return {}

    def extract_links(self):
        all_jobs = []
        page = 1
        while len(all_jobs) < self.max_jobs:
            data = self.fetch_jobs(page)
            jobs = data.get("data", [])
            logger.info(f"Fetched {len(jobs)} jobs from page {page}")
            if not jobs:
                break
            all_jobs.extend(jobs)
            if len(jobs) < self.page_size:
                break
            page += 1

        if not all_jobs:
            logger.warning("No jobs fetched. Exiting extraction.")
            return {}

        company_groups = defaultdict(list)
        for job in all_jobs[:self.max_jobs]:
            company_name = job.get("companyName", "unknown")
            company_groups[company_name].append(job)

        existing_job_urls = set()
        if not os.path.exists(self.output_csv):
            os.makedirs(os.path.dirname(self.output_csv), exist_ok=True,mode=0o777)

        if os.path.exists(self.output_csv) and os.path.getsize(self.output_csv) > 0:
            try:
                with open(self.output_csv, "r", encoding="utf-8") as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        existing_job_urls.add(row["job_url"])
            except Exception as e:
                logger.warning(f"Could not read existing CSV file: {e}")

        job_to_company_id = {}

        try:
            with open(self.output_csv, "a", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                if os.stat(self.output_csv).st_size == 0:
                    writer.writerow(["company_id","company_url","job_url", "content_selectors","source"])
                content_selectors_written = False

                for company_name, jobs in company_groups.items():
                    scraped_links = [
                        job.get("detailsPageUrl")
                        for job in jobs
                        if job.get("detailsPageUrl") and not job.get("detailsPageUrl", "").startswith("https://www.dice.com/apply-redirect")
                    ]
                    if not scraped_links:
                        continue

                    # company_url = jobs[0].get("companyUrl", "")
                    job_url = jobs[0].get("detailsPageUrl", "")
                    temp_company_url = None
                    if job_url:
                        temp_company_url = self.get_real_company_url(job_url)
                    company_id, _ = self.db.get_or_create_company(company_name, temp_company_url, {})
                    links_to_process = self.db.process_new_links(set(scraped_links), company_id)

                    for link in links_to_process or []:
                        if link in existing_job_urls:
                            logger.info(f"Link already exists, skipping: {link}")
                            continue
                        content_selectors = (
                            json.dumps({
                                "title": "h1",
                                "location": "li[data-cy=\"location\"]",
                                "skills": "div[data-cy=\"skillsList\"] span[id^=\"skillChip:\"]",
                                "job_description": "div.job-description",
                                "posted_at": "#timeAgo",
                                "company_name": "a[data-cy=\"companyNameLink\"]"
                            }, separators=(',', ':'))
                            if not content_selectors_written else ""
                        )
                        content_selectors_written = True
                        writer.writerow([company_id, temp_company_url, link, content_selectors, JobSource.DICE.value])
                        existing_job_urls.add(link)
                        job_to_company_id[link] = company_id
            logger.info(f"Extracted {len(all_jobs[:self.max_jobs])} jobs to {self.output_csv}")
        except Exception as e:
            logger.error(f"Failed to write CSV: {e}")

        return job_to_company_id

    def get_real_company_url(self, job_url):
        return asyncio.run(self._async_get_real_company_url(job_url))

    async def _async_get_real_company_url(self, job_url):
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                logger.info(f"Opening: {job_url}")
                

                try:
                    await page.goto(job_url,  wait_until="domcontentloaded", timeout=20000)
                    await page.wait_for_selector("a[data-cy='companyNameLink']", timeout=20000)
                    await page.click("a[data-cy='companyNameLink']")
                    await page.wait_for_timeout(2000)

                    await page.wait_for_selector("a.seds-text-utility-interaction", timeout=20000)
                    element = await page.query_selector("a.seds-text-utility-interaction")
                    website_link = await element.get_attribute("href") if element else None

                    logger.info(f"------web link--------->{website_link}")

                    if website_link and "dice.com" not in website_link:
                        return website_link
                except Exception as e:
                    logger.error(f"Failed to extract company URL from: {job_url} — {e}")
                finally:
                    await browser.close()

        except Exception as e:
            logger.error(f"Playwright error while processing {job_url}: {e}")
        return None



def main():
    settings = get_settings()
    DICE_API_KEY = os.getenv("DICE_API_KEY")
    # Step 1: Connect to the database
    db = PostgresLoader()
    db.connect()

    locations = [
    ("US", "United States"),
    ("CA", "Canada"),
    ("UK", "United Kingdom"),
    ("IN", "India"),
    ]
    #folder must be created
    os.makedirs(settings.LOCAL_SCRAPED_LINKS_DIR,exist_ok=True,mode=0o775)
    for country_code, location in locations:
        logger.info(f"Starting extraction for {location}")
        output_file = f"{settings.LOCAL_SCRAPED_LINKS_DIR}/dice_{country_code.lower()}_job_links.csv"
        extractor = DiceJobLinkExtractor(DICE_API_KEY, output_file, db, country_code=country_code, location=location)
        extractor.extract_links()
        db.close()