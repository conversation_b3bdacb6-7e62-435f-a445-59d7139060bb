from .usa_jd_link_extractor_script1 import main as usa_jd_link_extractor1
from .usa_jd_link_extractor_script2 import main as usa_jd_link_extractor2
from .usa_jd_link_extractor_script3 import main as usa_jd_link_extractor3
from .usa_jd_link_extractor_script4 import main as usa_jd_link_extractor4
from .usa_jd_link_extractor_script5 import main as usa_jd_link_extractor5
from .usa_jd_link_extractor_script6 import main as usa_jd_link_extractor6

async def main():
    # For testing - only run first script instead of all 6
    await usa_jd_link_extractor1()
    # usa_jd_link_extractor2()
    # usa_jd_link_extractor3()
    # usa_jd_link_extractor4()
    # usa_jd_link_extractor5()
    # usa_jd_link_extractor6()

if __name__ == "__main__":
    main()