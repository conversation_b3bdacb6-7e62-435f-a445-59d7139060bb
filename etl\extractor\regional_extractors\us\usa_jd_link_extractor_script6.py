import re
import os
import csv
from urllib.parse import urljoin, urlparse
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON>rror as PlaywrightError
import sys
import traceback
import asyncio
import logging

from config.core.settings import get_settings
from etl.loader.load_to_postgres import PostgresLoader

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- Configuration Loading ---

def load_configs_csv(filename: str):
    """Loads site configurations from a CSV file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(script_dir, filename)

    configs = {}
    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f"Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                if val is None:
                    return default_if_missing_or_none
                return val.strip()

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {}
                config_entry['start_url'] = get_safe_stripped_value(row, 'start_url')
                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue

                # Optional Locator Fields
                for loc_key_base in ["initial_wait_locator", "navigation_click_locator", "pagination_locator", "iframe_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]
                
                # String/Optional Fields with Defaults
                config_entry['pagination_type'] = get_safe_stripped_value(row, 'pagination_type', default_if_missing_or_none='none')
                config_entry['base_url'] = get_safe_stripped_value(row, 'base_url') or None # Explicit None if empty
                config_entry['link_filter_keyword'] = get_safe_stripped_value(row, 'link_filter_keyword') or None
                config_entry['link_extraction_method'] = get_safe_stripped_value(row, 'link_extraction_method', default_if_missing_or_none='href')
                config_entry['link_attribute'] = get_safe_stripped_value(row, 'link_attribute') or None
                config_entry['onclick_regex'] = get_safe_stripped_value(row, 'onclick_regex') or None
                config_entry['pagination_locator_template'] = get_safe_stripped_value(row, 'pagination_locator_template') or None
                config_entry['content_selectors'] = get_safe_stripped_value(row, 'content_selectors') or None # For CSV output
                config_entry['company_url'] = get_safe_stripped_value(row, 'company_url') or None # For CSV output
                config_entry['company_linkedin'] = get_safe_stripped_value(row, 'company_linkedin') or None # For CSV output


                # Integer Fields with Defaults
                for int_key, default_val in [
                    ('initial_wait_time', 5), ('initial_wait_timeout', 20),
                    ('navigation_wait_time', 5), ('page_load_timeout', 60),
                    ('pagination_wait_time', 5), ('max_pages', 50),
                    ('items_per_page_for_url_iteration', 25) # For url_iteration type
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val

                configs[company_name] = config_entry

        if not configs:
            logger.warning(f"No valid configurations were loaded from {filepath}.")
            return None # Indicates no usable configs
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        logger.error(f"Configuration file '{filepath}' not found.")
        raise # Re-raise to be caught by amain
    except Exception as e:
        logger.error(f"Unexpected error while loading CSV configurations from {filepath}: {e}")
        logger.error(traceback.format_exc())
        raise RuntimeError(f"Error loading CSV configurations: {e}") # Re-raise


# --- Helper Functions (Remain Synchronous) ---

def get_playwright_selector(locator_tuple):
    """Converts [strategy, value] list to Playwright selector string."""
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        logger.warning(f"Invalid locator_tuple format: {locator_tuple}. Assuming it's a direct selector string.")
        return str(locator_tuple)

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]:
        return f"css={value}"
    elif strategy_lower == "xpath":
        if not (value.startswith('/') or value.startswith('(') or value.startswith('.')):
            # logger.debug(f"XPath '{value}' might be unconventional (doesn't start with /, (, or .). Using it anyway.")
            pass # It's okay for XPath to not start with these e.g. "id('foo')" or relative paths within a context
        return f"xpath={value}"
    elif strategy_lower == "id":
        return f"id={value}" # Playwright's explicit id locator
    elif strategy_lower == "name":
         return f"[name='{value}']" # CSS attribute selector for name
    elif strategy_lower in ["link_text", "text"]:
         return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]:
         return f"text*={value}"
    elif strategy_lower in ["tag_name", "tag"]:
         return f"css={value}" # Treat as a CSS tag selector
    elif strategy_lower in ["class", "class_name"]:
        return f".{value.replace(' ', '.')}" # CSS class selector
    else:
        logger.warning(f"Unsupported locator strategy '{strategy}'. Using value as direct Playwright selector string: {value}")
        return str(value)

def resolve_url(base_url, link_url):
    """Resolves a potentially relative link URL against a base URL."""
    if not link_url:
        return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'):
        return None

    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"
    try:
        absolute_url = urljoin(base_url, link_url)
        if urlparse(absolute_url).scheme in ['http', 'https'] and urlparse(absolute_url).netloc:
            return absolute_url
        else:
            # logger.debug(f"Resolved URL '{absolute_url}' from base '{base_url}' and link '{link_url}' seems invalid. Skipping.")
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Core Asynchronous Scraping Function ---

async def scrape_job_links_async(site_name: str, configs: dict):
    """Scrapes job links for a specific site using Playwright async API."""
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set()
    start_time = asyncio.get_event_loop().time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f"Start URL: {config['start_url']}")

    browser = None
    context = None
    page = None # This will be the main page
    # current_scope will be either the main page or an iframe's content frame
    # Both Page and Frame objects have methods like .locator(), .evaluate(), .wait_for_load_state()
    current_scope = None

    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True, args=['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'])
        except PlaywrightError as launch_err:
            logger.error(f"Error launching browser for {site_name}: {launch_err}")
            logger.error("Ensure browser binaries are installed (run 'playwright install chromium')")
            return set()

        try:
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36',
                accept_downloads=False,
            )
            page = await context.new_page()
            current_scope = page # Initially, scope is the main page

            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms) # Applies to the page and its frames
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info("Navigating to start URL...")
            try:
                response = await page.goto(config['start_url'], wait_until='domcontentloaded', timeout=default_timeout_ms)
                if response and not response.ok:
                    logger.warning(f"Received non-OK status code {response.status} for {config['start_url']}")
                logger.info("Navigation successful.")
            except PlaywrightTimeoutError:
                logger.error(f"Timeout navigating to {config['start_url']} for {site_name}.")
                return set()
            except PlaywrightError as e:
                logger.error(f"Playwright navigation error for {site_name} to {config['start_url']}: {e}")
                return set()

            # --- IFRAME Handling ---
            if config.get("iframe_locator"):
                iframe_selector_str = get_playwright_selector(config["iframe_locator"])
                logger.info(f"Attempting to switch to iframe: {iframe_selector_str}")
                try:
                    # Ensure page is somewhat loaded before looking for iframe
                    await page.wait_for_load_state('domcontentloaded', timeout=default_timeout_ms // 2)
                    
                    # Get the ElementHandle for the iframe
                    frame_element_handle = await page.locator(iframe_selector_str).first.element_handle(timeout=default_timeout_ms // 2)
                    
                    if frame_element_handle:
                        iframe_content_frame = await frame_element_handle.content_frame() # This is synchronous
                        if iframe_content_frame:
                            current_scope = iframe_content_frame # current_scope is now the Frame object
                            # Wait for the frame to be meaningfully loaded
                            await current_scope.wait_for_load_state('domcontentloaded', timeout=default_timeout_ms // 2)
                            logger.info(f"Successfully focused on iframe '{iframe_selector_str}' and it has loaded.")
                        else:
                            logger.error(f"Could not get content_frame for iframe {iframe_selector_str}.")
                            return set()
                    else:
                        logger.error(f"iframe element {iframe_selector_str} not found using locator().element_handle().")
                        return set()
                except PlaywrightTimeoutError:
                    logger.error(f"Timeout finding or loading iframe {iframe_selector_str} for {site_name}.")
                    return set()
                except PlaywrightError as e:
                    logger.error(f"Error interacting with iframe {iframe_selector_str} for {site_name}: {e}")
                    return set()


            # --- Initial Action/Wait ---
            nav_clicked = False
            if config.get("navigation_click_locator") and config.get("pagination_type") == "click_to_navigate":
                nav_selector = get_playwright_selector(config["navigation_click_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                nav_wait_ms = config.get("navigation_wait_time", 5) * 1000
                try:
                    logger.info(f"Attempting initial navigation click: {nav_selector} (within current scope)")
                    nav_button = current_scope.locator(nav_selector).first
                    await nav_button.wait_for(state="visible", timeout=initial_wait_timeout_ms)
                    await nav_button.click(timeout=initial_wait_timeout_ms // 2)
                    logger.info(f"Navigation element clicked. Waiting {nav_wait_ms / 1000}s...")
                    await page.wait_for_timeout(nav_wait_ms) # Main page/context wait
                    nav_clicked = True
                except PlaywrightTimeoutError:
                    logger.warning(f"Timeout finding or clicking initial navigation element {nav_selector} for {site_name}.")
                except PlaywrightError as e:
                    logger.error(f"Error during initial navigation click for {site_name}: {e}")
                    return set()

            if config.get("initial_wait_locator") and not nav_clicked:
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {initial_wait_timeout_ms / 1000}s, within current scope)")
                    await current_scope.locator(wait_selector).first.wait_for(state='attached', timeout=initial_wait_timeout_ms)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial wait element {wait_selector} not found within timeout for {site_name}. Continuing...")
                except PlaywrightError as e:
                     logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")
            elif not config.get("navigation_click_locator") and not config.get("initial_wait_locator"):
                 initial_wait_ms = config.get("initial_wait_time", 5) * 1000
                 if initial_wait_ms > 0:
                     logger.info(f"Performing initial wait of {initial_wait_ms / 1000} seconds...")
                     await page.wait_for_timeout(initial_wait_ms) # Main page/context wait

            # --- Scraping and Pagination Loop ---
            current_page_num = 1
            max_pages = config.get("max_pages", 50)
            consecutive_no_new_links = 0

            # --- URL Iteration Pagination Type ---
            if config.get("pagination_type") == "url_iteration":
                logger.info(f"Using URL iteration pagination for {site_name}")
                start_url_base = config['start_url'].split('?')[0] if '?' in config['start_url'] else config['start_url']
                query_params_original = urlparse(config['start_url']).query
                
                page_param_template = config.get('pagination_locator_template', "&startrow={startrow}") # Default is example
                items_per_page = config.get('items_per_page_for_url_iteration', 25)

                for page_iter in range(max_pages): # 0 to max_pages-1
                    page_links_found_iter = set()
                    param_val_str = ""
                    if "{startrow}" in page_param_template:
                        param_val = page_iter * items_per_page
                        param_val_str = page_param_template.format(startrow=param_val).lstrip('&')
                    elif "{page_num}" in page_param_template:
                        # Assuming page_num is 1-indexed for templates, but iteration is 0-indexed
                        param_val = page_iter + 1 
                        param_val_str = page_param_template.format(page_num=param_val).lstrip('&')
                    else:
                        logger.error(f"pagination_locator_template for url_iteration needs {{startrow}} or {{page_num}} for {site_name}. Stopping.")
                        break
                    
                    current_iter_url = f"{start_url_base}"
                    # Construct URL carefully to avoid double '?' or incorrect param joining
                    if query_params_original and param_val_str:
                        # Avoid adding param if its key is already in original query (e.g. page=1 in start_url and template also gives page=N)
                        # This simple check might need refinement for complex cases.
                        if not param_val_str.split('=')[0] in query_params_original:
                           current_iter_url += f"?{query_params_original}&{param_val_str}"
                        else: # If param key exists, assume template is trying to override it or it's the main pager
                           current_iter_url += f"?{param_val_str}" # Or merge logic needed
                    elif param_val_str:
                         current_iter_url += f"?{param_val_str}"
                    elif query_params_original:
                        current_iter_url += f"?{query_params_original}"
                    # If no query_params_original and no param_val_str, it's just start_url_base (first page implicitly)

                    logger.info(f"Navigating to URL iteration page {page_iter + 1}: {current_iter_url}")
                    try:
                        # URL iteration always navigates the main page, not an iframe directly by URL
                        await page.goto(current_iter_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                        await page.wait_for_timeout(config.get("pagination_wait_time", 5) * 1000) # Static wait after load
                    except PlaywrightTimeoutError:
                        logger.warning(f"Timeout navigating to {current_iter_url}. Assuming end of pages.")
                        break
                    except PlaywrightError as e:
                        logger.error(f"Error navigating to {current_iter_url}: {e}. Stopping iteration.")
                        break

                    job_link_selector = get_playwright_selector(config["job_link_locator"])
                    try:
                        # Wait for links within the current_scope (page or frame)
                        await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                        job_elements = await current_scope.locator(job_link_selector).all()
                        if not job_elements and page_iter > 0 : # Don't break on first iter if no links
                            logger.info(f"No job elements found on this URL iteration page ({current_iter_url}). Assuming end of pages.")
                            break
                        logger.info(f"Found {len(job_elements)} potential link elements on URL iteration {page_iter + 1}.")

                        base_url_for_resolve = config.get("base_url") or current_iter_url # Use iter_url as base
                        for element in job_elements:
                            # Link extraction logic reused from main loop (see below)
                            raw_link = None
                            link_extraction_method = config.get("link_extraction_method", "href")
                            link_attribute = config.get("link_attribute")
                            onclick_regex_str = config.get("onclick_regex")
                            onclick_regex_compiled_iter = None
                            if link_extraction_method == "onclick_regex" and onclick_regex_str:
                                try: onclick_regex_compiled_iter = re.compile(onclick_regex_str)
                                except re.error: pass # Handled again later if needed

                            if link_extraction_method == "href":
                                raw_link = await element.get_attribute("href")
                            elif link_extraction_method == "attribute" and link_attribute:
                                raw_link = await element.get_attribute(link_attribute)
                            elif link_extraction_method == "onclick_regex" and onclick_regex_compiled_iter:
                                onclick_attr = await element.get_attribute("onclick")
                                if onclick_attr:
                                    match = onclick_regex_compiled_iter.search(onclick_attr)
                                    if match: raw_link = match.group(1) if match.groups() else match.group(0)
                            elif link_extraction_method == "span_id_constructor":
                                job_id = await element.get_attribute(link_attribute or "data-jobid")
                                if job_id and base_url_for_resolve: # base_url_for_resolve is current_iter_url
                                    raw_link = f"{base_url_for_resolve.rstrip('/')}/#{job_id.lstrip('#')}"
                            else: # Fallback or unknown
                                raw_link = await element.get_attribute("href")

                            absolute_link = resolve_url(base_url_for_resolve, raw_link)
                            if absolute_link:
                                link_filter_keyword = config.get("link_filter_keyword")
                                if link_filter_keyword:
                                    if link_filter_keyword.startswith("NOT:"):
                                        keyword_to_exclude = link_filter_keyword[4:]
                                        if keyword_to_exclude in absolute_link: continue
                                    elif link_filter_keyword not in absolute_link: continue
                                page_links_found_iter.add(absolute_link)
                    
                    except PlaywrightTimeoutError:
                        logger.warning(f"No job links found (or timed out waiting) on {current_iter_url}. Assuming end of pages for URL iteration.")
                        break # Exit URL iteration loop
                    except PlaywrightError as e:
                        logger.error(f"Error locating/processing job links on {current_iter_url}: {e}")
                        break # Exit URL iteration loop

                    if not page_links_found_iter and page_iter > 0: # If not first page and no links
                         logger.info(f"No links extracted from {current_iter_url}, assuming end of URL iteration.")
                         break
                    
                    newly_added_this_iter = len(page_links_found_iter - all_job_links)
                    logger.info(f"Found {len(page_links_found_iter)} unique links on this iteration, {newly_added_this_iter} are new.")
                    all_job_links.update(page_links_found_iter)
                    if newly_added_this_iter == 0 and page_iter > 0 and len(job_elements) > 0: # If we found elements but no new links
                        consecutive_no_new_links +=1
                        if consecutive_no_new_links >=2:
                            logger.info(f"URL Iteration: No new links for {consecutive_no_new_links} iterations. Stopping.")
                            break
                    else:
                        consecutive_no_new_links = 0


            # --- Standard Pagination Loop (if not URL iteration) ---
            else: # All other pagination types
                while current_page_num <= max_pages:
                    logger.info(f"Scraping page/view {current_page_num} (within current scope)...")
                    page_links_found = set()
                    # Small delay for dynamic content settling in the current scope
                    await current_scope.wait_for_timeout(1500) 

                    try:
                        job_link_selector = get_playwright_selector(config["job_link_locator"])
                        link_extraction_method = config.get("link_extraction_method", "href")
                        link_attribute = config.get("link_attribute")
                        onclick_regex_str = config.get("onclick_regex")
                        link_filter_keyword = config.get("link_filter_keyword")
                        
                        # Determine base URL for resolving links: config's base_url, or current page/frame URL
                        # current_scope.url is what we need. Page has .url, Frame has .url
                        base_url_for_resolve = config.get("base_url") or current_scope.url

                        onclick_regex_compiled = None
                        if link_extraction_method == "onclick_regex" and onclick_regex_str:
                            try:
                                onclick_regex_compiled = re.compile(onclick_regex_str)
                            except re.error as re_err:
                                logger.warning(f"Invalid onclick_regex '{onclick_regex_str}'. Falling back to 'href'. Error: {re_err}")
                                link_extraction_method = "href" # Fallback

                        job_elements = []
                        try:
                            await current_scope.locator(job_link_selector).first.wait_for(state='attached', timeout=15000)
                            job_elements = await current_scope.locator(job_link_selector).all()
                            logger.info(f"Found {len(job_elements)} potential link elements on page {current_page_num}.")
                        except PlaywrightTimeoutError:
                            logger.warning(f"Timeout waiting for job links ({job_link_selector}) on page {current_page_num}.")
                            if current_page_num == 1 and not all_job_links: 
                                logger.warning(f"No job links found on the first page for {site_name}. Check locators and scope (main page / iframe).")
                            if config.get("pagination_type") != 'scroll': break # If not scrolling, and no links, probably done
                        except PlaywrightError as e:
                             logger.error(f"Error locating job links ({job_link_selector}) on page {current_page_num}: {e}")
                             break

                        if not job_elements and config.get("pagination_type") != 'scroll':
                             if current_page_num > 1: logger.info("No more job link elements found. Ending pagination.")
                             break

                        for i, element in enumerate(job_elements):
                            raw_link = None
                            try:
                                if link_extraction_method == "href":
                                    raw_link = await element.get_attribute("href")
                                elif link_extraction_method == "attribute" and link_attribute:
                                    raw_link = await element.get_attribute(link_attribute)
                                elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                                    onclick_attr = await element.get_attribute("onclick")
                                    if onclick_attr:
                                        match = onclick_regex_compiled.search(onclick_attr)
                                        if match: raw_link = match.group(1) if match.groups() else match.group(0) # Use group 1 if exists, else full match
                                elif link_extraction_method == "span_id_constructor":
                                    job_id = await element.get_attribute(link_attribute or "data-jobid") # Use configured or default attribute
                                    if job_id and base_url_for_resolve:
                                        # Ensure base_url_for_resolve doesn't already end with #
                                        clean_base = base_url_for_resolve.split('#')[0]
                                        raw_link = f"{clean_base.rstrip('/')}/#{job_id.lstrip('#')}"
                                else: # Fallback or unknown method
                                    raw_link = await element.get_attribute("href") # Default to href
                                    if link_extraction_method not in ["href", "attribute", "onclick_regex", "span_id_constructor"]:
                                         logger.warning(f"Unknown link_extraction_method '{link_extraction_method}'. Defaulting to 'href'.")

                                absolute_link = resolve_url(base_url_for_resolve, raw_link)

                                if absolute_link:
                                    if link_filter_keyword:
                                        if link_filter_keyword.startswith("NOT:"):
                                            keyword_to_exclude = link_filter_keyword[4:]
                                            if keyword_to_exclude in absolute_link:
                                                continue # Skip this link
                                        elif link_filter_keyword not in absolute_link:
                                            continue # Skip this link
                                    page_links_found.add(absolute_link)
                            except PlaywrightError as el_err: logger.error(f"Error processing element {i+1} on page {current_page_num}: {el_err}")
                            except Exception as e: logger.error(f"Unexpected error processing element {i+1}: {e}")
                    
                    except Exception as page_err: # Catch broader errors during page processing
                        logger.error(f"Error during link extraction on page {current_page_num} for {site_name}: {page_err}")
                        logger.error(traceback.format_exc())
                        break # Stop processing this site on major error

                    newly_added_count = len(page_links_found - all_job_links)
                    logger.info(f"Found {len(page_links_found)} unique links on this page/view, {newly_added_count} are new.")
                    all_job_links.update(page_links_found)

                    pagination_type = config.get("pagination_type", "none")
                    pagination_wait_ms = config.get("pagination_wait_time", 5) * 1000

                    if pagination_type in ["load_more", "load_more_js", "scroll", "load_more_scroll"]:
                        # Only increment no_new_links if we actually found elements on the page but they yielded no *new* links
                        if newly_added_count == 0 and current_page_num > 1 and len(job_elements) > 0: 
                             consecutive_no_new_links += 1
                             logger.info(f"No new links found on page/view {current_page_num}. Consecutive count: {consecutive_no_new_links}")
                             if consecutive_no_new_links >= 2:
                                  logger.info(f"Stopping {pagination_type} pagination for {site_name} after {consecutive_no_new_links} attempts with no new links.")
                                  break
                        else:
                            consecutive_no_new_links = 0 # Reset if new links were found or no elements to begin with

                    pagination_successful = False
                    if pagination_type == "none" or (pagination_type == "click_to_navigate" and nav_clicked):
                        logger.info(f"Pagination type is '{pagination_type}'. Stopping pagination loop.")
                        break
                    elif pagination_type == "scroll":
                        logger.info("Scrolling down (within current scope)...")
                        last_height = await current_scope.evaluate("document.body.scrollHeight")
                        await current_scope.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                        await page.wait_for_timeout(pagination_wait_ms + 1000) # Main page wait for scroll effects
                        new_height = await current_scope.evaluate("document.body.scrollHeight")
                        if new_height == last_height and current_page_num > 1 : # Check if scroll actually happened
                            logger.info("Scroll height did not change. Assuming end of content.")
                            break
                        else:
                             logger.info(f"Scrolled from {last_height} to {new_height}. Proceeding to scrape new view.")
                             pagination_successful = True
                    elif pagination_type in ["next_button", "load_more", "next_button_js", "load_more_js", "next_button_url", "next_button_data_table", "load_more_scroll", "next_button_js_scroll"]:
                        pag_selector_tuple = config.get("pagination_locator")
                        pag_selector_template = config.get("pagination_locator_template")
                        final_pag_selector = None

                        if pag_selector_tuple:
                            final_pag_selector = get_playwright_selector(pag_selector_tuple)
                        elif pagination_type == "next_button_data_table" and pag_selector_template:
                            try:
                                next_page_num_for_selector = current_page_num + 1
                                # Assume XPath for templates usually, but get_playwright_selector handles it
                                final_pag_selector = get_playwright_selector(['XPATH', pag_selector_template.format(page_num=next_page_num_for_selector)])
                                logger.info(f"Using pagination template for page {next_page_num_for_selector}: {final_pag_selector}")
                            except Exception as fmt_err:
                                 logger.error(f"Error formatting pagination template for page {next_page_num_for_selector}: {fmt_err}")
                                 break
                        if not final_pag_selector:
                            logger.error(f"Pagination type '{pagination_type}' requires 'pagination_locator' or a valid 'pagination_locator_template'. Stopping.")
                            break
                        try:
                            logger.info(f"Looking for pagination element: {final_pag_selector} (within current scope)")
                            pagination_element_locator = current_scope.locator(final_pag_selector).first
                            await pagination_element_locator.wait_for(state='attached', timeout=10000)

                            if not await pagination_element_locator.is_visible(timeout=5000):
                                logger.info("Pagination element found but not visible. Assuming end of pages.")
                                break
                            if not await pagination_element_locator.is_enabled(timeout=5000): # Check if clickable
                                logger.info("Pagination element found but not enabled. Assuming end of pages.")
                                break
                            
                            if pagination_type == "next_button_url":
                                 next_url = await pagination_element_locator.get_attribute('href')
                                 # Resolve against the main page's URL, as navigation happens there
                                 resolved_next_url = resolve_url(page.url, next_url)
                                 if resolved_next_url and resolved_next_url != page.url and "javascript:void(0)" not in resolved_next_url.lower():
                                     logger.info(f"Navigating main page to next page URL: {resolved_next_url}")
                                     await page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                     pagination_successful = True
                                     # If content was in an iframe, current_scope might need to be re-established
                                     # This is a complex case; for now, assume next_button_url implies main page content or iframe src updates
                                     if config.get("iframe_locator"):
                                         logger.warning("next_button_url used with iframe; iframe scope might need re-initialization after page.goto. Manual check advised.")
                                         # Potentially re-run iframe focusing logic here if needed.
                                 else:
                                     logger.info("Next button found but no valid/different URL. Assuming end.")
                                     break
                            else: # Click-based pagination
                                logger.info(f"Clicking pagination element ({pagination_type}) (within current scope).")
                                await pagination_element_locator.scroll_into_view_if_needed(timeout=5000)
                                
                                if "js" in pagination_type: # JS click
                                    await pagination_element_locator.evaluate("element => element.click()")
                                else: # Regular click
                                    await pagination_element_locator.click(timeout=10000)

                                logger.info(f"Pagination element clicked. Waiting {pagination_wait_ms / 1000}s...")
                                await page.wait_for_timeout(pagination_wait_ms) # Main page/context wait
                                if 'js' in pagination_type or 'scroll' in pagination_type or 'load_more' in pagination_type : # Wait for content to potentially load
                                   await current_scope.wait_for_load_state('domcontentloaded', timeout=15000)
                                pagination_successful = True
                        except PlaywrightTimeoutError:
                            logger.warning(f"Pagination element '{final_pag_selector}' not found or not interactable within timeout. Assuming end.")
                            break
                        except PlaywrightError as pag_err:
                            logger.error(f"Error interacting with pagination element '{final_pag_selector}': {pag_err}")
                            break
                        except Exception as e: # Catch any other unexpected error during pagination
                            logger.error(f"Unexpected error during pagination action: {e}")
                            logger.error(traceback.format_exc())
                            break
                    else: # Unknown pagination type
                        logger.error(f"Unknown pagination_type '{pagination_type}'. Stopping.")
                        break

                    if not pagination_successful and pagination_type != "scroll": # Scroll success is determined by height change
                        logger.info(f"Pagination action failed for type '{pagination_type}'. Stopping.")
                        break
                    
                    current_page_num += 1
                    if current_page_num > max_pages:
                        logger.info(f"Reached max pages limit ({max_pages}) for {site_name}.")
                        break
            # End of while/for loop for pagination

        except PlaywrightError as e: # Catch critical Playwright errors not caught by specific handlers
            logger.error(f"A critical Playwright error occurred for {site_name}: {e}")
            logger.error(traceback.format_exc())
            # Ensure cleanup, though `async with` should handle browser.
            # Return empty set on critical error to allow other sites to process.
            return set()
        except Exception as e: # Catch any other unexpected errors during the main scraping logic
            logger.error(f"An unexpected error occurred during scraping for {site_name}: {e}")
            logger.error(traceback.format_exc())
            return set()
        # `async with` handles closing browser, context, page on normal exit or if an exception propagates out of this block.
        # The return set() statements above will cause early exit from the `try` block, and `async with` will clean up.

    duration = asyncio.get_event_loop().time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f"Found {len(all_job_links)} unique links.")
    return all_job_links

async def limited_scrape(semaphore, company_name, site_configs):
    async with semaphore:
        return await scrape_job_links_async(company_name, site_configs)

# --- Main Asynchronous Execution ---
async def amain():
    settings = get_settings()
    db = PostgresLoader()
    db.connect()
    config_file = "usa_site_configs/site_config6.csv"
    sites = None  # if you wanna specific company
    output_file = f"{settings.LOCAL_SCRAPED_LINKS_DIR}/usa_job_links6.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            # Error messages are printed within load_configs_csv
            sys.exit(1) # Exit if config loading fails
    except Exception:
         sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with async_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")
        # Optionally exit if check fails, or just warn
        # sys.exit(1)

    batch_size = 10
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

        results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))


    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i]  # Get the result for the current company
        config: dict = site_configs[company_name]  # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set()  # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")

        # Skip storing in the database if no links are found
        if not scraped_links:
            logger.info(f"No job links found for {company_name}. Skipping database storage.")
            continue

        # Check the company exists or create it
        company_id, created = db.get_or_create_company(company_name, config.get("company_url", ""), config)
        logger.info(f"Company ID: {company_id}, {'created' if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # It should be unique links
        links_to_process = db.process_new_links(links_to_process, company_id)

        # Skip storing in the database if no unique links are found after filtering
        if not links_to_process:
            logger.info(f"No unique job links to process for {company_name}. Skipping database storage.")
            continue

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': str(content_selectors_for_site) if content_selectors_for_site else None,
                })
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': '',
                })
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv # Increment total for CSV count


    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")
    # You might want to return the output file path or something else useful
    # return output_file # Changed to return None as main doesn't necessarily need to return the path

def main():
    asyncio.run(amain())

if __name__ == "__main__":
    main()