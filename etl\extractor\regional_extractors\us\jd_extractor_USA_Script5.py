import asyncio
import re
import os
import csv
from urllib.parse import urljoin, urlparse
from playwright.sync_api import sync_playwright, <PERSON>out<PERSON><PERSON><PERSON> as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
import argparse
import sys
import traceback
import time

from config.core import logger

from etl.extractor.regional_extractors.uk.career_page_uk_extractor import scrape_job_links_async
from etl.loader.load_to_postgres import PostgresLoader

# --- Configuration Loading ---

def load_configs_csv(filename="site_config_USA_Script(5).csv"):
    """Loads site configurations from a CSV file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # If filename itself is an absolute path, os.path.join will use it directly.
    # If filename is relative, it will be joined with script_dir.
    filepath = os.path.join(script_dir, filename)
    # However, if filename is intended to be an absolute path from the -c argument,
    # and the user provides one, we should honor that.
    # A simple check: if filename starts with a drive letter (Windows) or '/' (Unix-like)
    if os.path.isabs(filename):
        filepath = filename # Use the absolute path directly if provided via -c

    configs = {}

    logger.debug(f"--- DEBUG INFO for Config Loading ---")
    logger.debug(f"Script's __file__ variable: {__file__}")
    logger.debug(f"Absolute path of the script: {os.path.abspath(__file__)}")
    logger.debug(f"Directory of the script (script_dir): {script_dir}")
    logger.debug(f"Filename argument received by load_configs_csv: '{filename}'")
    logger.debug(f"Is '{filename}' an absolute path? {os.path.isabs(filename)}")
    logger.debug(f"Final filepath being attempted for CSV: '{filepath}'")
    logger.debug(f"Does this filepath exist (os.path.exists)? {os.path.exists(filepath)}")
    logger.debug(f"--- END DEBUG INFO ---")

    logger.info(f"Attempting to load configurations from {filepath}")
    try:
        with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            if not reader.fieldnames:
                logger.error(f" Error: CSV file '{filepath}' is empty or has no header.")
                return None

            required_headers = ['company_name', 'start_url', 'job_link_locator_strategy', 'job_link_locator_value']
            missing_headers = [h for h in required_headers if h not in reader.fieldnames]
            if missing_headers:
                logger.error(f"Error: CSV file '{filepath}' is missing essential header columns: {', '.join(missing_headers)}. Cannot proceed.")
                return None

            def get_safe_stripped_value(row_dict, key, default_if_missing_or_none=''):
                val = row_dict.get(key)
                return val.strip() if val is not None else default_if_missing_or_none

            for i, row in enumerate(reader):
                company_name = get_safe_stripped_value(row, 'company_name')
                if not company_name:
                    logger.warning(f"Skipping row {i+2} in {filepath} due to missing or empty 'company_name'.")
                    continue

                config_entry = {
                    'company_name': company_name,
                    'enabled': get_safe_stripped_value(row, 'enabled', 'TRUE').upper() == 'TRUE',
                    'headless': get_safe_stripped_value(row, 'headless', 'TRUE').upper() == 'TRUE',
                    'start_url': get_safe_stripped_value(row, 'start_url'),
                    'link_extraction_method': get_safe_stripped_value(row, 'link_extraction_method', 'href') or 'href',
                    'link_attribute': get_safe_stripped_value(row, 'link_attribute') or None,
                    'onclick_regex': get_safe_stripped_value(row, 'onclick_regex') or None,
                    'base_url': get_safe_stripped_value(row, 'base_url') or None,
                    'link_filter_keyword': get_safe_stripped_value(row, 'link_filter_keyword') or None,
                    'pagination_type': get_safe_stripped_value(row, 'pagination_type', 'none') or 'none',
                    'pagination_locator_template': get_safe_stripped_value(row, 'pagination_locator_template') or None,
                    'content_selectors': get_safe_stripped_value(row, 'content_selectors') or None,
                }

                if not config_entry['start_url']:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing or empty 'start_url'.")
                    continue

                jl_strategy = get_safe_stripped_value(row, 'job_link_locator_strategy')
                jl_value = get_safe_stripped_value(row, 'job_link_locator_value')
                if jl_strategy and jl_value:
                    config_entry['job_link_locator'] = [jl_strategy, jl_value]
                else:
                    logger.warning(f"Skipping site '{company_name}' (row {i+2}) due to missing 'job_link_locator_strategy' or 'job_link_locator_value'.")
                    continue

                for loc_key_base in ["initial_wait_locator", "navigation_click_locator", "pagination_locator"]:
                    strategy = get_safe_stripped_value(row, f"{loc_key_base}_strategy")
                    value = get_safe_stripped_value(row, f"{loc_key_base}_value")
                    if strategy and value:
                        config_entry[loc_key_base] = [strategy, value]

                for int_key, default_val in [
                    ('page_load_timeout', 60), ('initial_wait_time', 5),
                    ('initial_wait_timeout', 20), ('navigation_wait_time', 5),
                    ('pagination_wait_time', 5), ('max_pages', 50)
                ]:
                    val_str = get_safe_stripped_value(row, int_key)
                    try:
                        config_entry[int_key] = int(val_str) if val_str else default_val
                    except ValueError:
                        logger.warning(f"Invalid integer value '{val_str}' for '{int_key}' in site '{company_name}' (row {i+2}). Using default {default_val}.")
                        config_entry[int_key] = default_val

                configs[company_name] = config_entry

        if not configs:
            logger.error(f"No valid configurations were loaded from {filepath}.")
            return None
        logger.info(f"Successfully loaded {len(configs)} configurations from {filepath}")
        return configs

    except FileNotFoundError:
        # The debug prints above will have already shown the problematic path
        logger.error(f" Error: Configuration file '{filepath}' not found (FileNotFoundError). Please check the path and filename.")
        sys.exit(1) # Exit if config file is not found
    except Exception as e:
        logger.error(f" Unexpected error while loading CSV configurations from {filepath}: {e}")
        traceback.print_exc()
        sys.exit(1)


# --- Helper Functions ---

def get_playwright_selector(locator_tuple):
    if not isinstance(locator_tuple, (list, tuple)) or len(locator_tuple) != 2:
        return str(locator_tuple)

    strategy, value = locator_tuple
    strategy_lower = strategy.lower().replace(" ", "_").replace("-", "_")

    if strategy_lower in ["css", "css_selector"]: return f"css={value}"
    elif strategy_lower == "xpath": return f"xpath={value}"
    elif strategy_lower == "id": return f"#{value}"
    elif strategy_lower == "name": return f"[name='{value}']"
    elif strategy_lower in ["link_text", "text"]: return f"text={value}"
    elif strategy_lower in ["partial_link_text", "text_contains"]: return f"text*={value}"
    elif strategy_lower in ["tag_name", "tag"]: return f"{value}"
    elif strategy_lower in ["class", "class_name"]: return f".{value.replace(' ', '.')}"
    else:
        logger.warning(f"Warning: Unsupported locator strategy '{strategy}'. Using value as CSS: css={value}")
        return f"css={value}"

def resolve_url(base_url, link_url):
    if not link_url: return None
    link_url = link_url.strip()
    if not link_url or link_url.lower().startswith('javascript:'): return None

    if link_url.startswith("//"):
        parsed_base = urlparse(base_url)
        scheme = parsed_base.scheme if parsed_base.scheme else 'https'
        return f"{scheme}:{link_url}"

    try:
        absolute_url = urljoin(base_url, link_url)
        parsed_abs = urlparse(absolute_url)
        if parsed_abs.scheme in ['http', 'https'] and parsed_abs.netloc:
            return absolute_url
        else:
            return None
    except Exception as e:
        logger.error(f"Error resolving URL: base='{base_url}', link='{link_url}'. Error: {e}")
        return None

# --- Core Scraping Function ---

def scrape_job_links_playwright(site_name, configs):
    if site_name not in configs:
        logger.error(f"Configuration for site '{site_name}' not found.")
        return set()

    config = configs[site_name]
    all_job_links = set()
    start_time = time.time()

    logger.info(f"\n--- Starting scrape for: {site_name} ---")
    logger.info(f" Start URL: {config['start_url']}")

    browser = None
    context = None
    page = None

    try:
        with sync_playwright() as p:
            try:
                browser = p.chromium.launch(
                    headless=config.get('headless', True),
                    args=['--no-sandbox', '--disable-setuid-sandbox']
                )
            except PlaywrightError as launch_err:
                logger.error(f"Error launching browser for {site_name}: {launch_err}. Run 'playwright install'.")
                return set()

            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
                accept_downloads=False,
            )
            page = context.new_page()
            default_timeout_ms = config.get("page_load_timeout", 60) * 1000
            page.set_default_timeout(default_timeout_ms)
            page.set_default_navigation_timeout(default_timeout_ms)

            logger.info(f"Navigating to start URL...")
            try:
                response = page.goto(config['start_url'], wait_until='domcontentloaded', timeout=default_timeout_ms)
                if response and not response.ok:
                    logger.warning(f"Received non-OK status code {response.status} for {config['start_url']}")
                logger.info("Navigation successful.")
            except PlaywrightTimeoutError:
                logger.error(f"Timeout navigating to {config['start_url']} for {site_name}.")
                return set()
            except PlaywrightError as e:
                logger.error(f"Playwright navigation error for {site_name} to {config['start_url']}: {e}")
                return set()

            if config.get("initial_wait_locator"):
                wait_selector = get_playwright_selector(config["initial_wait_locator"])
                initial_wait_timeout_ms = config.get("initial_wait_timeout", 20) * 1000
                try:
                    logger.info(f"Waiting for initial element: {wait_selector} (timeout: {initial_wait_timeout_ms / 1000}s)")
                    page.locator(wait_selector).first.wait_for(state='attached', timeout=initial_wait_timeout_ms)
                    logger.info("Initial element found.")
                except PlaywrightTimeoutError:
                    logger.warning(f"Initial wait element {wait_selector} not found for {site_name}. Continuing...")
                except PlaywrightError as e:
                     logger.error(f"Error waiting for initial element {wait_selector}: {e}. Continuing...")

            initial_wait_s = config.get("initial_wait_time", 0)
            if initial_wait_s > 0:
                logger.info(f"Performing initial fixed wait of {initial_wait_s} seconds...")
                page.wait_for_timeout(initial_wait_s * 1000)

            current_page_num = 1
            max_pages = config.get("max_pages", 50)
            consecutive_no_new_links = 0

            while current_page_num <= max_pages:
                logger.info(f"Scraping page/view {current_page_num}...")
                page.wait_for_timeout(1500)
                page_links_found_this_iteration = set()

                try:
                    job_link_selector_str = get_playwright_selector(config["job_link_locator"])
                    link_extraction_method = config.get("link_extraction_method", "href")
                    link_attribute = config.get("link_attribute")
                    onclick_regex_str = config.get("onclick_regex")

                    current_base_url = config.get("base_url") if config.get("base_url") else page.url
                    if current_base_url and not (current_base_url.endswith('/') or current_base_url.endswith('=')) and '?' not in urlparse(current_base_url).path and not urlparse(current_base_url).query:
                        if not os.path.splitext(urlparse(current_base_url).path)[1]:
                             current_base_url += '/'

                    link_filter_keywords_str = config.get("link_filter_keyword")
                    link_filter_keywords = [kw.strip() for kw in link_filter_keywords_str.split(',')] if link_filter_keywords_str else []

                    onclick_regex_compiled = None
                    if link_extraction_method == "onclick_regex" and onclick_regex_str:
                        try:
                            onclick_regex_compiled = re.compile(onclick_regex_str)
                        except re.error as re_err:
                            logger.warning(f"Invalid onclick_regex for {site_name}: {re_err}. Defaulting to href.")
                            link_extraction_method = "href"

                    job_elements = []
                    try:
                        job_link_wait_timeout_ms = config.get("initial_wait_timeout", 10) * 1000
                        if current_page_num > 1:
                            job_link_wait_timeout_ms = max(job_link_wait_timeout_ms, config.get("pagination_wait_time", 5) * 1000 + 5000)

                        logger.info(f" Waiting for job links ({job_link_selector_str}) with timeout {job_link_wait_timeout_ms/1000}s")
                        page.locator(job_link_selector_str).first.wait_for(state='attached', timeout=job_link_wait_timeout_ms)
                        job_elements = page.locator(job_link_selector_str).all()
                        logger.info(f" Found {len(job_elements)} potential link elements on page {current_page_num}.")
                    except PlaywrightTimeoutError:
                        logger.warning(f" Timeout waiting for job links ({job_link_selector_str}) on page {current_page_num}.")
                        if current_page_num == 1 and not all_job_links:
                            logger.warning(f" No job links found on the first page for {site_name}. Check locators/timing.")
                        if config.get("pagination_type", "none") != 'scroll':
                            break
                    except PlaywrightError as e:
                         logger.error(f" Error locating job links ({job_link_selector_str}) on page {current_page_num}: {e}")
                         break

                    if not job_elements and config.get("pagination_type", "none") != 'scroll':
                         if current_page_num > 1:
                              logger.info("No more job link elements found. Ending pagination.")
                         break

                    for i, element in enumerate(job_elements):
                        raw_link = None
                        try:
                            if link_extraction_method == "href":
                                raw_link = element.get_attribute("href")
                            elif link_extraction_method == "attribute" and link_attribute:
                                raw_link = element.get_attribute(link_attribute)
                            elif link_extraction_method == "onclick_regex" and onclick_regex_compiled:
                                onclick_attr = element.get_attribute("onclick")
                                if onclick_attr:
                                    match = onclick_regex_compiled.search(onclick_attr)
                                    if match:
                                        raw_link = match.group(1) if match.groups() else match.group(0)
                            else:
                                raw_link = element.get_attribute("href")
                                if link_extraction_method not in ["href", "attribute", "onclick_regex"]:
                                     logger.warning(f"Unknown link_extraction_method '{link_extraction_method}'. Defaulting to 'href'.")

                            absolute_link = resolve_url(current_base_url, raw_link)

                            if absolute_link:
                                if absolute_link == config['start_url']:
                                    continue

                                passes_filter = not link_filter_keywords
                                if link_filter_keywords:
                                    for keyword in link_filter_keywords:
                                        if keyword in absolute_link:
                                            passes_filter = True
                                            break

                                if passes_filter:
                                    page_links_found_this_iteration.add(absolute_link)

                        except PlaywrightError as el_err:
                            logger.error(f"  Error processing element {i+1} on page {current_page_num}: {el_err}")
                        except Exception as e:
                            logger.error(f"  Unexpected error processing element {i+1} on page {current_page_num}: {e}")

                except Exception as page_err:
                    logger.error(f"Error during link extraction on page {current_page_num} for {site_name}: {page_err}")
                    traceback.print_exc()
                    break

                newly_added_this_iteration = page_links_found_this_iteration - all_job_links
                logger.info(f" Found {len(page_links_found_this_iteration)} unique links on this iteration, {len(newly_added_this_iteration)} are new.")
                all_job_links.update(page_links_found_this_iteration)

                pagination_type = config.get("pagination_type", "none")
                pagination_wait_s = config.get("pagination_wait_time", 5)

                if pagination_type in ["load_more", "scroll", "next_button_js_scroll"]:
                    if not newly_added_this_iteration and current_page_num > 1:
                         consecutive_no_new_links += 1
                         logger.info(f" No new links found for this {pagination_type} iteration. Consecutive count: {consecutive_no_new_links}")
                         if consecutive_no_new_links >= 2:
                              logger.info(f"Stopping {pagination_type} pagination for {site_name} after {consecutive_no_new_links} attempts with no new links.")
                              break
                    else:
                        consecutive_no_new_links = 0

                pagination_successful = False
                if pagination_type == "none":
                    logger.info(f"Pagination type is 'none'. Stopping pagination loop.")
                    break

                elif pagination_type == "scroll":
                    logger.info("Scrolling down...")
                    page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
                    page.wait_for_timeout((pagination_wait_s + 1) * 1000)
                    pagination_successful = True

                elif pagination_type in ["next_button", "load_more", "next_button_js", "next_button_url"]:
                    pag_selector_str = None
                    if config.get("pagination_locator"):
                        pag_selector_str = get_playwright_selector(config["pagination_locator"])

                    if not pag_selector_str:
                        logger.error(f"Pagination type '{pagination_type}' requires 'pagination_locator'. Stopping.")
                        break

                    try:
                        logger.info(f"Looking for pagination element: {pag_selector_str}")
                        pagination_element = page.locator(pag_selector_str)

                        pagination_element.wait_for(state='attached', timeout=10000)

                        if not pagination_element.is_visible(timeout=5000):
                            logger.info(" Pagination element found but not visible. Assuming end of pages.")
                            break
                        if pagination_element.is_disabled(timeout=1000):
                            logger.info(" Pagination element is disabled. Assuming end of pages.")
                            break

                        if pagination_type == "next_button_url":
                             next_url = pagination_element.get_attribute('href')
                             resolved_next_url = resolve_url(page.url, next_url)
                             if resolved_next_url and resolved_next_url != page.url and "javascript:void(0)" not in resolved_next_url.lower() :
                                 logger.info(f" Navigating to next page URL: {resolved_next_url}")
                                 page.goto(resolved_next_url, wait_until='domcontentloaded', timeout=default_timeout_ms)
                                 pagination_successful = True
                             else:
                                 logger.info(" Next button URL not valid, same as current, or javascript:void(0). Assuming end.")
                                 break
                        else:
                            logger.info(f" Clicking pagination element ({pagination_type})...")
                            pagination_element.scroll_into_view_if_needed(timeout=3000)
                            pagination_element.click(timeout=10000)
                            if pagination_wait_s > 0:
                                logger.info(f" Waiting {pagination_wait_s}s after pagination click...")
                                page.wait_for_timeout(pagination_wait_s * 1000)
                            else:
                                try:
                                    page.wait_for_load_state('networkidle', timeout=15000)
                                except PlaywrightTimeoutError:
                                    logger.info(" Timeout waiting for networkidle after click, trying domcontentloaded.")
                                    page.wait_for_load_state('domcontentloaded', timeout=10000)
                            pagination_successful = True

                    except PlaywrightTimeoutError:
                        logger.info(f" Pagination element '{pag_selector_str}' not found or not interactable within timeout. Assuming end.")
                        break
                    except PlaywrightError as pag_err:
                        logger.error(f" Error interacting with pagination element '{pag_selector_str}': {pag_err}")
                        traceback.print_exc()
                        break
                    except Exception as e:
                        logger.error(f" Unexpected error during pagination action: {e}")
                        traceback.print_exc()
                        break

                else:
                    logger.error(f"Unknown pagination_type '{pagination_type}'. Stopping.")
                    break

                if not pagination_successful and pagination_type != "scroll":
                    logger.info(f"Pagination action failed for type '{pagination_type}'. Stopping.")
                    break

                current_page_num += 1

            if current_page_num > max_pages:
                logger.info(f"Reached max pages limit ({max_pages}) for {site_name}.")

    except PlaywrightError as e:
        logger.error(f"A critical Playwright error occurred for {site_name}: {e}")
        traceback.print_exc()
    except Exception as e:
        logger.error(f"An unexpected error occurred during scraping for {site_name}: {e}")
        traceback.print_exc()
    finally:
        if page:
            try:
                page.close()
            except Exception as page_close_err:
                logger.error(f"Error closing page for {site_name}: {page_close_err}")
        if context:
            try:
                context.close()
            except Exception as context_close_err:
                logger.error(f"Error closing context for {site_name}: {context_close_err}")
        if browser and browser.is_connected():
            try:
                logger.info(f"Closing browser for {site_name}.")
                browser.close()
            except Exception as ex_close:
                logger.error(f"Error closing browser for {site_name}: {ex_close}")

    duration = time.time() - start_time
    logger.info(f"--- Finished scrape for: {site_name} in {duration:.2f} seconds ---")
    logger.info(f" Found {len(all_job_links)} unique links.")
    return all_job_links
# --- Main Execution ---
async def amain():

    db = PostgresLoader()
    db.connect()
    config_file = "site_config_USA_Script(5).csv"
    sites = None  # if you wanna specific company
    output_file = "data/scraped_job_links/uk_job_links5.csv"

    # Load configurations - this part can remain synchronous
    try:
        site_configs = load_configs_csv(config_file)
        if not site_configs:
            logger.error("Failed to load site configurations.")
            sys.exit(1) # Exit if config loading fails
    except Exception as e:
        logger.error(f"Exception during config loading: {e}")
        sys.exit(1) # Exit on any exception during config loading

    if sites:
        # Filter sites to scrape based on command-line input
        sites_to_scrape = []
        invalid_sites = []
        for site_arg in sites:
            if site_arg in site_configs:
                sites_to_scrape.append(site_arg)
            else:
                invalid_sites.append(site_arg)

        if invalid_sites:
            logger.warning(f"The following specified sites were not found in the config file and will be skipped: {', '.join(invalid_sites)}")
        if not sites_to_scrape:
            logger.error("No valid sites specified to scrape were found in the configuration.")
            sys.exit(1)
        logger.info(f"Scraping specified sites: {', '.join(sites_to_scrape)}")
    else:
        # Scrape all sites defined in the config file
        sites_to_scrape = list(site_configs.keys())
        logger.info(f"Scraping all {len(sites_to_scrape)} sites defined in the configuration file.")

    all_results_for_csv = []
    summary_counts = {}
    total_links_saved_to_csv = 0 # Renamed for clarity, this is the count *in the CSV*

    logger.info("Ensuring Playwright browsers are installed (run 'playwright install' if you encounter issues)...")
    # Attempt a quick check using async playwright
    try:
        async with sync_playwright() as p:
            # Use await for launch and close
            browser = await p.chromium.launch(headless=True)
            await browser.close()
        logger.info("Playwright chromium check successful.")
    except Exception as install_err:
        logger.error(f"Playwright check failed: {install_err}. Please run 'playwright install'.")

    batch_size = 5
    batch_run = (len(sites_to_scrape) + batch_size-1) // batch_size
    batches = []
    for i in range(0,batch_run):
        batches.append(sites_to_scrape[i*batch_size:min(len(sites_to_scrape),(i+1)*batch_size)])

    results = []
    for batch in batches:
        tasks = [scrape_job_links_async(name, site_configs) for name in batch]
        results.extend(await asyncio.gather(*tasks))

    # Iterate through the results and process them
    for i, company_name in enumerate(sites_to_scrape):
        result = results[i]  # Get the result for the current company
        config: dict = site_configs[company_name]  # Get config for this company

        if isinstance(result, Exception):
            # Handle scraping errors for this specific site
            logger.error(f"Scraping task failed for {company_name}: {result}")
            scraped_links = set()  # Treat as 0 links found for summary and CSV
        else:
            # Task succeeded, result is the set of links
            scraped_links = result

        # Capture the number of links found for this company for the summary
        summary_counts[company_name] = len(scraped_links)
        logger.info(f"Links found for {company_name}: {len(scraped_links)}")

        # Skip storing in the database if no links are found
        if not scraped_links:
            logger.info(f"No job links found for {company_name}. Skipping database storage.")
            continue

        # Check the company exists or create it
        company_id, created = db.get_or_create_company(company_name, config.get("company_url", ""), config)
        logger.info(f"Company ID: {company_id}, {'created' if created else 'already exists'}")

        # Apply your filter logic here
        links_to_process = set(scraped_links)
        # It should be unique links
        links_to_process = db.process_new_links(links_to_process, company_id)

        # Skip storing in the database if no unique links are found after filtering
        if not links_to_process:
            logger.info(f"No unique job links to process for {company_name}. Skipping database storage.")
            continue

        # Get config details for CSV output
        content_selectors_for_site = config.get('content_selectors')
        link_count_for_csv = 0
        first_iteration = True
        # Use links_to_process after applying your filter logic
        for link in sorted(list(links_to_process)):
            if first_iteration:
                # First row has full details
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': str(content_selectors_for_site) if content_selectors_for_site else None,
                })
                first_iteration = False
            else:
                # Subsequent rows have empty company_name for cleaner CSV
                all_results_for_csv.append({
                    "company_id": company_id,
                    'job_url': link,
                    'content_selectors': '',
                })
            link_count_for_csv += 1

        total_links_saved_to_csv += link_count_for_csv  # Increment total for CSV count

    logger.info("==============================")
    logger.info(f"Total unique links saved to CSV across {len(sites_to_scrape)} site(s): {total_links_saved_to_csv}")
    logger.info("==============================")

    if all_results_for_csv:
        try:
            # Ensure the output directory exists if a path is specified
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True, mode=0o755)
                logger.info(f"Created output directory: {output_dir}")

            with open(output_file, "w", newline='', encoding='utf-8') as f:
                # Define fieldnames including the potentially empty content_selectors, company_url, company_linkedin
                fieldnames = ['company_id', 'job_url', 'content_selectors']
                writer = csv.DictWriter(f, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(all_results_for_csv)
            logger.info(f"Results successfully saved to {output_file}")
        except IOError as e:
            logger.error(f"Could not write to output file '{output_file}'. Check permissions or path. Error: {e}")
            logger.debug("Stack trace:", exc_info=True)
        except Exception as e:
            logger.error(f"Error saving results to CSV file '{output_file}': {e}")
            logger.debug("Stack trace:", exc_info=True)
    else:
        logger.info("No links were scraped successfully. Output file will not be created.")

    logger.info("Script finished.")

def main():
    asyncio.run(amain())

if __name__ == "__main__":
    main()
